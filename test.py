import sys
from PyPDF2 import PdfReader

def analyze_acroform_fields(pdf_path):
    try:
        # 读取PDF文件
        reader = PdfReader(pdf_path)
        
        # 检查是否有AcroForm
        if '/AcroForm' not in reader.root_object:
            print("PDF文件中没有AcroForm信息")
            return []
        
        acroform = reader.root_object['/AcroForm']
        
        # 检查是否有字段
        if '/Fields' not in acroform:
            print("AcroForm中没有字段")
            return []
        
        fields = acroform['/Fields']
        results = []
        
        for field in fields:
            field_obj = field.get_object()
            
            # 检查是否有numberOfCells属性
            if '/numberOfCells' in field_obj:
                name = field_obj.get('/T', '无名字段')
                num_cells = field_obj['/numberOfCells']
                
                # 获取字段宽度
                rect = field_obj.get('/Rect', [0, 0, 0, 0])
                width = abs(rect[2] - rect[0])  # 计算宽度 (x2 - x1)
                
                # 计算字间距 (假设每个字符平均宽度为1，实际可能需要调整)
                # 这里简化计算：字间距 = (字段宽度 - numberOfCells) / (numberOfCells - 1)
                if num_cells > 1:
                    char_spacing = (width - num_cells) / (num_cells - 1)
                else:
                    char_spacing = 0
                
                results.append({
                    'name': name,
                    'numberOfCells': num_cells,
                    'field_width': width,
                    'char_spacing': char_spacing
                })
        
        return results
    
    except Exception as e:
        print(f"处理PDF时出错: {str(e)}")
        return []

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("请提供PDF文件路径作为参数")
        sys.exit(1)
    
    pdf_path = sys.argv[1]
    results = analyze_acroform_fields(pdf_path)
    
    if results:
        print("找到以下设置了numberOfCells属性的字段:")
        print("{:<30} {:<15} {:<15} {:<15}".format(
            "字段名", "numberOfCells", "字段宽度", "建议字间距"))
        print("-" * 75)
        
        for item in results:
            print("{:<30} {:<15} {:<15.2f} {:<15.2f}".format(
                str(item['name']),
                item['numberOfCells'],
                item['field_width'],
                item['char_spacing']))
    else:
        print("没有找到设置了numberOfCells属性的字段")