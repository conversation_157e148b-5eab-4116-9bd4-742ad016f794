#!/usr/bin/env python3
"""
Verification script to check if Tc values were correctly written to PDF
"""

import sys
import PyPDF2
from PyPDF2.generic import DictionaryObject, ArrayObject, IndirectObject, NameObject

def verify_tc_in_pdf(pdf_path):
    """Verify that Tc values are present in the PDF form fields"""
    try:
        with open(pdf_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            
            # Get AcroForm
            if '/AcroForm' not in reader.trailer['/Root']:
                print("No AcroForm found in PDF")
                return
                
            acroform = reader.trailer['/Root']['/AcroForm']
            
            if '/Fields' not in acroform:
                print("No fields found in AcroForm")
                return
                
            print(f"Checking PDF: {pdf_path}")
            print("=" * 50)
            
            fields_with_tc = []
            total_fields = 0
            
            def check_field_tc(field_obj, parent_name=""):
                nonlocal total_fields, fields_with_tc
                
                if isinstance(field_obj, IndirectObject):
                    field_obj = field_obj.get_object()
                    
                if not isinstance(field_obj, DictionaryObject):
                    return
                    
                total_fields += 1
                
                # Get field name
                field_name = ""
                t_key = NameObject('/T')
                if t_key in field_obj:
                    field_name = str(field_obj[t_key])
                
                # Build full name
                if parent_name:
                    full_name = f"{parent_name}.{field_name}" if field_name else parent_name
                else:
                    full_name = field_name
                
                # Check DA string for Tc
                da_key = NameObject('/DA')
                if da_key in field_obj:
                    da_string = str(field_obj[da_key])
                    if 'Tc' in da_string:
                        # Extract Tc value
                        parts = da_string.split()
                        tc_value = None
                        for i, part in enumerate(parts):
                            if part == 'Tc' and i > 0:
                                try:
                                    tc_value = float(parts[i-1])
                                    break
                                except ValueError:
                                    pass
                        
                        fields_with_tc.append({
                            'name': full_name,
                            'da_string': da_string,
                            'tc_value': tc_value
                        })
                        
                        print(f"✓ Field: {full_name}")
                        print(f"  DA: {da_string}")
                        print(f"  Tc value: {tc_value}")
                        print("-" * 30)
                
                # Check child fields
                kids_key = NameObject('/Kids')
                if kids_key in field_obj:
                    kids = field_obj[kids_key]
                    if isinstance(kids, IndirectObject):
                        kids = kids.get_object()
                    if isinstance(kids, ArrayObject):
                        for kid in kids:
                            check_field_tc(kid, full_name)
            
            # Process all fields
            fields = acroform['/Fields']
            if isinstance(fields, IndirectObject):
                fields = fields.get_object()
            if isinstance(fields, ArrayObject):
                for field in fields:
                    check_field_tc(field)
            
            print(f"\nSummary:")
            print(f"Total fields processed: {total_fields}")
            print(f"Fields with Tc values: {len(fields_with_tc)}")
            
            if fields_with_tc:
                print(f"\nFields with Tc values:")
                for field in fields_with_tc:
                    print(f"  {field['name']}: Tc = {field['tc_value']}")
            else:
                print("No fields with Tc values found")
                
    except Exception as e:
        print(f"Error verifying PDF: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    else:
        pdf_path = "fw9_with_Tc.pdf"
    
    verify_tc_in_pdf(pdf_path)
