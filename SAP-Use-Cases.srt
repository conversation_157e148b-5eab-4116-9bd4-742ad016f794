1
00:00:00,000 --> 00:00:24,800
然后这个项目列表将被锁定。

2
00:00:24,800 --> 00:00:27,720
因为你输入了这个，然后我们知道什么是PM。

3
00:00:28,360 --> 00:00:32,000
没有这个，这是空的。

4
00:00:32,000 --> 00:00:35,440
所以这个输入将触发这个习惯。

5
00:00:35,440 --> 00:00:43,400
然后在这个下拉菜单中，你也只需选择这个项目。

6
00:00:43,400 --> 00:00:45,440
你可以选择多个项目。

7
00:00:45,440 --> 00:00:47,680
你的意思是选择多个。

8
00:00:47,680 --> 00:00:57,640
你的意思是你可能收到数量三。

9
00:00:57,640 --> 00:00:59,440
你可能收到二，收到三。

10
00:00:59,440 --> 00:01:03,080
然后三，你收到四。

11
00:01:03,080 --> 00:01:05,880
所以这有点像一个-

12
00:01:05,880 --> 00:01:09,240
不，但我们可以将其映射到表单的不同部分。

13
00:01:09,240 --> 00:01:11,440
今天，我们如何进行扫描是

14
00:01:11,440 --> 00:01:14,320
我们有重复的部分。

15
00:01:14,320 --> 00:01:19,320
但这种动态方式很难-

16
00:01:19,320 --> 00:01:21,520
不，我们可以一次选择一个项目。

17
00:01:21,520 --> 00:01:22,240
那没关系。

18
00:01:22,240 --> 00:01:24,360
是的，所以这意味着如果你选择一个，

19
00:01:24,360 --> 00:01:27,680
你会自动创建另一个项目。

20
00:01:27,680 --> 00:01:28,880
如果你想选择，你就选择。

21
00:01:28,880 --> 00:01:30,440
如果你不选择，那就出去了。

22
00:01:30,440 --> 00:01:31,240
是的，是的，是的，是的。

23
00:01:31,240 --> 00:01:32,480
是的，你完成了。

24
00:01:32,480 --> 00:01:35,960
另一种方法是给你一个表格。

25
00:01:35,960 --> 00:01:37,640
是的。

26
00:01:37,640 --> 00:01:40,520
然后因为选择了这个，显示这个表格。

27
00:01:40,520 --> 00:01:44,040
项目一，项目二，项目三。

28
00:01:44,040 --> 00:01:46,800
然后你只需在这里填写数字。

29
00:01:46,800 --> 00:01:49,800
是的，是的，是的，是的。

30
00:01:49,800 --> 00:01:51,800
Stanley，有一个评论。

31
00:01:51,800 --> 00:01:55,040
表格，通常，体验不是很好，

32
00:01:55,040 --> 00:01:57,720
特别是在较小的设备上，对吧？

33
00:01:57,720 --> 00:02:03,360
所以人们解决这个问题的另一种方法是你将其视为一个-

34
00:02:03,360 --> 00:02:06,360
对，你为一个部分带来一个新的控制台，像页面一样。

35
00:02:06,360 --> 00:02:08,360
这是页面的较小版本。

36
00:02:08,360 --> 00:02:10,200
你可以有任意多的字段。

37
00:02:10,200 --> 00:02:11,600
这里会有一个添加按钮。

38
00:02:11,600 --> 00:02:12,720
所以它会不断重复。

39
00:02:12,720 --> 00:02:14,120
是的，是的。

40
00:02:14,120 --> 00:02:15,520
更用户友好。

41
00:02:15,520 --> 00:02:16,120
和移动端。

42
00:02:16,120 --> 00:02:16,920
是的，是的。

43
00:02:16,920 --> 00:02:18,400
我们可以做到。

44
00:02:18,400 --> 00:02:21,480
因为这意味着在错误的时间，这个表单在那里。

45
00:02:21,480 --> 00:02:22,480
你可以。

46
00:02:22,480 --> 00:02:23,960
这是一个错误时间表单。

47
00:02:23,960 --> 00:02:26,360
是的，这就是我的想法。

48
00:02:26,360 --> 00:02:32,440
这里的关键是关于这个输入导致这个部分。

49
00:02:32,440 --> 00:02:33,920
是的。

50
00:02:33,920 --> 00:02:36,960
然后你有错误的时间。

51
00:02:36,960 --> 00:02:38,720
这是一个非常好的事情。

52
00:02:38,760 --> 00:02:40,640
那是一个用户只会添加。

53
00:02:40,640 --> 00:02:41,120
他会添加。

54
00:02:41,120 --> 00:02:42,560
再次，他会选择新项目，对吧？

55
00:02:42,560 --> 00:02:43,560
是的，是的，是的。

56
00:02:43,560 --> 00:02:44,440
这是错误的时间。

57
00:02:44,440 --> 00:02:47,000
是的，我们的工作在那个项目填充中结束。

58
00:02:47,000 --> 00:02:49,880
再买一个，请。

59
00:02:49,880 --> 00:02:51,640
是的，这听起来像一年的交易。

60
00:02:51,640 --> 00:02:52,520
是的，是的。

61
00:02:52,520 --> 00:02:54,720
好吧，我还有一个问题。

62
00:02:54,720 --> 00:02:56,920
这可能不只是一个已经下拉的项目。

63
00:02:56,920 --> 00:02:59,400
你可能需要为我一个项目。

64
00:02:59,400 --> 00:03:00,040
是的，是的，是的。

65
00:03:00,040 --> 00:03:00,520
正确。

66
00:03:00,520 --> 00:03:01,440
所以这是一个数组，对吧？

67
00:03:01,440 --> 00:03:02,560
不只是一个返回。

68
00:03:02,560 --> 00:03:03,120
正确，正确。

69
00:03:03,120 --> 00:03:08,440
所以对于这个项目，我们需要一个新的行项目，对吧？

70
00:03:09,440 --> 00:03:14,160
好的，让我说说我们今天是如何建模的，对吧？

71
00:03:14,160 --> 00:03:22,240
所以PO行项目有大约10个字段。

72
00:03:22,240 --> 00:03:25,520
父级是PO文档，对吧？

73
00:03:25,520 --> 00:03:28,720
所以这是PO号码，对吧？

74
00:03:28,720 --> 00:03:31,240
这有，比如说，一些项目。

75
00:03:31,240 --> 00:03:33,240
所以这里有一些字段，这里有一些字段。

76
00:03:33,240 --> 00:03:35,920
但我们将所有这些字段都放入这个。

77
00:03:35,920 --> 00:03:36,920
你理解了，对吧？

78
00:03:36,960 --> 00:03:41,120
所以我们得到采购订单。

79
00:03:41,120 --> 00:03:44,640
假设采购订单有五个项目，对吧？

80
00:03:44,640 --> 00:03:49,120
我们在表中创建五行表，对吧？

81
00:03:49,120 --> 00:03:50,960
采购订单行项目表。

82
00:03:50,960 --> 00:03:52,840
我们创建五条记录。

83
00:03:52,840 --> 00:03:54,440
这就是这个部分的全部内容。

84
00:03:54,440 --> 00:03:58,600
但我们会重复来自标题的信息。

85
00:03:58,600 --> 00:04:00,120
是的，所以有一个问题，Bala。

86
00:04:00,120 --> 00:04:01,840
是的。

87
00:04:01,840 --> 00:04:04,080
不确定我理解。

88
00:04:04,080 --> 00:04:05,520
让我给你展示一下。

89
00:04:05,520 --> 00:04:09,400
也许我们可以做一个演示，然后回到这个模型。

90
00:04:09,400 --> 00:04:11,720
这将显示数据库本身，

91
00:04:11,720 --> 00:04:12,920
我们是如何设计它们的，对吧？

92
00:04:12,920 --> 00:04:14,120
先做一个演示。

93
00:04:14,120 --> 00:04:15,600
好的，好的。

94
00:04:15,600 --> 00:04:17,920
这将有助于理解。

95
00:04:17,920 --> 00:04:20,280
你也可以分享屏幕，Bala。

96
00:04:20,280 --> 00:04:22,000
分享屏幕，Bala。

97
00:04:22,000 --> 00:04:25,720
如果你可以连接到那个。

98
00:04:25,720 --> 00:04:26,200
伦敦。

99
00:04:33,160 --> 00:04:35,440
谢谢你，Bala。

100
00:04:36,440 --> 00:04:37,920
轮椅停了。

101
00:04:43,400 --> 00:04:44,400
是的，它应该工作。

102
00:04:44,400 --> 00:04:45,880
是的，现在很好。

103
00:04:45,880 --> 00:04:47,360
是的。

104
00:04:47,360 --> 00:04:48,840
现在你看到了，不是吗？

105
00:04:48,840 --> 00:04:52,080
是的，一切都在工作。

106
00:04:52,080 --> 00:04:58,400
所以让我们，好的，让我从SAP方面展示我的意思。

107
00:04:58,400 --> 00:05:00,080
我会跳过采购申请。

108
00:05:00,080 --> 00:05:00,960
那没关系，对吧？

109
00:05:00,960 --> 00:05:02,720
采购申请你想要通过吗？

110
00:05:02,720 --> 00:05:05,120
是的，也许我们可以切断整个队列。

111
00:05:05,600 --> 00:05:08,960
所以它从PR开始，对吧？

112
00:05:08,960 --> 00:05:10,320
它会进入菜单。

113
00:05:10,320 --> 00:05:13,080
是的。

114
00:05:13,080 --> 00:05:17,160
采购申请将是创建PO的过程。

115
00:05:17,160 --> 00:05:19,400
是的，它将是初步文档。

116
00:05:19,400 --> 00:05:21,000
你想在SAP中创建PO吗？

117
00:05:21,000 --> 00:05:21,800
不是直接的。

118
00:05:21,800 --> 00:05:25,640
所以会发生的是可能有多个PR。

119
00:05:25,640 --> 00:05:28,400
不，它会作为SAP中的采购请求对象。

120
00:05:28,400 --> 00:05:28,880
哦，好的。

121
00:05:28,880 --> 00:05:30,200
所以采购需要。

122
00:05:30,200 --> 00:05:33,440
是的，它在SAP中创建一个采购请求对象。

123
00:05:33,520 --> 00:05:36,960
现在，采购人员可以整合采购请求

124
00:05:36,960 --> 00:05:40,840
来自不同部门，如果他说，

125
00:05:40,840 --> 00:05:44,800
我要把所有这些放入一个采购订单，

126
00:05:44,800 --> 00:05:47,800
在供应商方面，你可以这样做。

127
00:05:47,800 --> 00:05:52,040
所以在这里我会审查它并加载表单。

128
00:05:54,680 --> 00:05:57,200
所以这只不过是哪个部门

129
00:05:57,200 --> 00:05:58,560
我在为其工作，对吧？

130
00:05:58,560 --> 00:06:00,400
这是哪个部门？

131
00:06:00,400 --> 00:06:02,040
这实际上来自SAP。

132
00:06:02,040 --> 00:06:04,320
理想情况下，它来自SAP。

133
00:06:04,320 --> 00:06:08,760
这也是来自SAP的主数据。

134
00:06:08,760 --> 00:06:11,120
实际上有两个字段我们合并为一个，

135
00:06:11,120 --> 00:06:12,840
这里是一样的。

136
00:06:12,840 --> 00:06:16,800
一个是该部门的代码。

137
00:06:16,800 --> 00:06:18,280
还有描述。

138
00:06:18,280 --> 00:06:19,720
对我们来说，没有智能，对吧？

139
00:06:19,720 --> 00:06:21,840
这都是我们如何获得可以显示的数据，对吧？

140
00:06:21,840 --> 00:06:22,320
是的。

141
00:06:22,320 --> 00:06:23,760
这更多是为了让这个人理解。

142
00:06:23,760 --> 00:06:24,760
正确。

143
00:06:24,760 --> 00:06:27,760
对于第二个，他们可以依赖第一个。

144
00:06:27,760 --> 00:06:29,800
是的，是的，是的。

145
00:06:29,800 --> 00:06:30,560
是的。

146
00:06:30,560 --> 00:06:31,320
不，不，不，不。

147
00:06:31,320 --> 00:06:32,640
供应不依赖于那个。

148
00:06:32,640 --> 00:06:33,280
对不起，先生。

149
00:06:33,280 --> 00:06:33,840
独立的。

150
00:06:33,840 --> 00:06:34,480
独立的。

151
00:06:34,480 --> 00:06:37,680
因为它应该列出这个被获得了。

152
00:06:37,680 --> 00:06:38,520
不，不，不。

153
00:06:38,520 --> 00:06:41,440
对于这个用例来说，那不存在。

154
00:06:41,440 --> 00:06:43,880
你可能在其他用例中有那个。

155
00:06:43,880 --> 00:06:46,400
因为在ERP中，这很常见。

156
00:06:46,400 --> 00:06:47,160
是的。

157
00:06:47,160 --> 00:06:49,640
所以假设我选择一个日期。

158
00:06:49,640 --> 00:06:51,120
这都是标准的。

159
00:06:51,120 --> 00:06:53,120
现在，这也来自SAP。

160
00:06:53,120 --> 00:06:55,480
这个项目代码和项目描述。

161
00:06:55,480 --> 00:06:57,680
所以这个，再次，我们必须连接

162
00:06:57,680 --> 00:07:00,800
因为我们没有依赖的下拉菜单。

163
00:07:00,840 --> 00:07:03,280
Stanley，对于你说的例子，对吧？

164
00:07:03,280 --> 00:07:08,600
理想情况下，应该发生的是S001是第一个下拉菜单。

165
00:07:08,600 --> 00:07:11,160
然后基于那个，第二个描述

166
00:07:11,160 --> 00:07:13,400
会被选择，或者相反。

167
00:07:13,400 --> 00:07:15,000
我可能选择描述。

168
00:07:15,000 --> 00:07:18,920
因为那是，那真的是一对多还是一对一？

169
00:07:18,920 --> 00:07:19,760
只有一对一。

170
00:07:19,760 --> 00:07:20,480
一对一。

171
00:07:20,480 --> 00:07:22,280
这是项目代码和项目描述。

172
00:07:22,280 --> 00:07:24,600
在这种情况下，这是一个安全的方法。

173
00:07:24,600 --> 00:07:27,520
但如果你在S001上有多个质量

174
00:07:27,520 --> 00:07:30,560
要选择或其他东西，它变得棘手，对吧？

175
00:07:30,800 --> 00:07:31,400
是的，不，不。

176
00:07:31,400 --> 00:07:33,080
在这种情况下，它是一对一的。

177
00:07:33,080 --> 00:07:35,160
实际上，当我们与Arihanth讨论

178
00:07:35,160 --> 00:07:39,320
关于那个外部管理的列表时，我们认为一个项目

179
00:07:39,320 --> 00:07:41,280
可以有多个列。

180
00:07:41,280 --> 00:07:45,120
所以在下拉菜单中，你选择哪个是主键，

181
00:07:45,120 --> 00:07:46,600
我的意思是，主列。

182
00:07:46,600 --> 00:07:49,280
但你也可以连接其他列

183
00:07:49,280 --> 00:07:50,640
在下拉菜单中显示。

184
00:07:50,640 --> 00:07:54,120
你可以说，下拉菜单，你显示1，2，3，4，

185
00:07:54,120 --> 00:07:59,120
所有组合在一起，也许用逗号或某种分隔符。

186
00:07:59,120 --> 00:08:02,000
但代码是这个。

187
00:08:02,000 --> 00:08:04,520
这意味着选择了这个代码。

188
00:08:04,520 --> 00:08:06,800
因为当我们发送回SAP时，

189
00:08:06,800 --> 00:08:10,480
我们不需要再次发送项目描述。

190
00:08:10,480 --> 00:08:14,360
这是因为一个人必须理解那是什么部分。

191
00:08:14,360 --> 00:08:16,320
他不能只通过项目代码。

192
00:08:16,320 --> 00:08:18,880
他实际上只会知道描述，

193
00:08:18,880 --> 00:08:20,760
说它是镀锌的。

194
00:08:20,760 --> 00:08:22,560
他们真的在SAP之外这样做吗，

195
00:08:22,560 --> 00:08:24,280
这么多数据在SAP中？

196
00:08:24,280 --> 00:08:26,160
有人会如何这样提出？

197
00:08:26,160 --> 00:08:27,040
他们这样做。

198
00:08:28,000 --> 00:08:30,440
我实际上有Excel。

199
00:08:30,440 --> 00:08:31,720
谁会上传到SAP？

200
00:08:31,720 --> 00:08:32,480
好的。

201
00:08:32,480 --> 00:08:33,600
好问题。

202
00:08:33,600 --> 00:08:35,880
所以现在，在现实世界中发生什么，对吧？

203
00:08:39,640 --> 00:08:42,680
我们实际上有来自他们的数据。

204
00:08:48,080 --> 00:08:50,200
这实际上是他们如何提出的。

205
00:08:50,200 --> 00:08:51,560
所以这是一个简单的表单。

206
00:08:51,560 --> 00:08:52,960
是的。

207
00:08:52,960 --> 00:08:56,040
Stanley是实际的采购请求表单。

208
00:08:56,080 --> 00:08:59,040
所以这是部门。

209
00:08:59,040 --> 00:09:01,320
他们手动分配号码。

210
00:09:01,320 --> 00:09:06,000
PO号码，也是，他们事后记录，对吧？

211
00:09:06,000 --> 00:09:09,000
所以采购部门会签名。

212
00:09:09,000 --> 00:09:11,920
他们会输入PO号码并签名。

213
00:09:11,920 --> 00:09:13,280
供应商号码，供应商名称。

214
00:09:13,280 --> 00:09:15,440
所以有人会给他们项目的主数据。

215
00:09:15,440 --> 00:09:15,920
正确。

216
00:09:15,920 --> 00:09:16,760
项目描述。

217
00:09:16,760 --> 00:09:18,560
这是手动的。

218
00:09:18,560 --> 00:09:21,840
然后他们会在SAP中手动输入，基本上，

219
00:09:21,840 --> 00:09:23,120
在批准之后。

220
00:09:23,120 --> 00:09:25,320
即使他们要在这里输入，他们

221
00:09:25,320 --> 00:09:26,600
需要参考一些表格，不是吗？

222
00:09:26,600 --> 00:09:27,120
是的，是的，是的。

223
00:09:27,120 --> 00:09:28,160
他们怎么知道项目是什么？

224
00:09:28,160 --> 00:09:28,800
是的，是的，是的。

225
00:09:28,800 --> 00:09:30,400
他们实际上必须参考一些打印件。

226
00:09:30,400 --> 00:09:31,680
另一个主数据。

227
00:09:31,680 --> 00:09:33,120
是的，是的，是的。

228
00:09:33,120 --> 00:09:36,040
但看，这就是我们实际上在数字化的。

229
00:09:36,040 --> 00:09:38,240
所以我们不能给他们另一个主数据。

230
00:09:38,240 --> 00:09:39,400
硬编码列表，是的。

231
00:09:39,400 --> 00:09:39,920
是的，是的。

232
00:09:39,920 --> 00:09:43,040
所以我的意思是，他们已经发送给我们一切，Stanley。

233
00:09:43,040 --> 00:09:46,560
看，他们已经发送给我们采购订单，格式。

234
00:09:46,560 --> 00:09:47,120
好的。

235
00:09:47,120 --> 00:09:48,080
他们今天如何做。

236
00:09:48,080 --> 00:09:49,240
那是表格，Stanley。

237
00:09:49,240 --> 00:09:51,200
他说，项目数量。

238
00:09:51,200 --> 00:09:52,800
它可以有任意数量的部分。

239
00:09:52,800 --> 00:09:54,160
是的。

240
00:09:54,200 --> 00:09:57,520
这也是非常可定制的，取决于业务。

241
00:09:57,520 --> 00:09:59,840
他们真正需要的，它有列，对吧？

242
00:09:59,840 --> 00:10:00,640
是的，是的，是的。

243
00:10:00,640 --> 00:10:03,240
我们不能在那里强制任何格式。

244
00:10:03,240 --> 00:10:04,960
所以这是材料请求。

245
00:10:04,960 --> 00:10:06,480
这是材料请求表。

246
00:10:06,480 --> 00:10:07,800
尺寸和规格。

247
00:10:07,800 --> 00:10:10,080
这个和采购请求之间的唯一区别，

248
00:10:10,080 --> 00:10:11,920
这也是一个采购请求，Stanley。

249
00:10:11,920 --> 00:10:15,640
唯一的事情是，这可能来自库存，

250
00:10:15,640 --> 00:10:17,840
而不是成为采购订单。

251
00:10:17,840 --> 00:10:19,720
所以你现在不用担心那个。

252
00:10:19,720 --> 00:10:20,240
好的。

253
00:10:20,240 --> 00:10:21,560
我的意思是，让我们暂时不管那个。

254
00:10:21,560 --> 00:10:24,680
我的意思是，这只是在SAP中发生的事情，

255
00:10:24,680 --> 00:10:30,120
无论是从库存中给出还是那种添加

256
00:10:30,120 --> 00:10:31,960
在SAP中发生。

257
00:10:31,960 --> 00:10:34,480
所以现在，这都是可选的。

258
00:10:34,480 --> 00:10:38,040
这只是那里的一个镜像，这个表单。

259
00:10:38,040 --> 00:10:42,040
所以我会说，我想要30件。

260
00:10:42,040 --> 00:10:44,600
这就是Anu说的，Stanley。

261
00:10:44,600 --> 00:10:46,680
现在我们通过隐藏逻辑做到了。

262
00:10:46,680 --> 00:10:49,240
我们通过条件逻辑做到了。

263
00:10:49,240 --> 00:10:50,800
条件逻辑，是的。

264
00:10:50,840 --> 00:10:54,440
现在我可以更改第二个项目。

265
00:10:54,440 --> 00:10:59,760
然后我可以在这里输入数量，好的，40。

266
00:10:59,760 --> 00:11:04,480
计量单位也将是SAP数据。

267
00:11:04,480 --> 00:11:07,280
然后我会提交这个。

268
00:11:07,280 --> 00:11:08,720
好的。

269
00:11:08,720 --> 00:11:10,480
现在它去采购部门。

270
00:11:10,480 --> 00:11:12,320
如果你看手动表单，他们必须

271
00:11:12,320 --> 00:11:15,480
批准并填写PO号码等等。

272
00:11:15,480 --> 00:11:18,720
现在，他们只想要从这个得到批准。

273
00:11:18,720 --> 00:11:26,280
所以我们说审查，我们就暂停。

274
00:11:29,920 --> 00:11:33,920
这个采购请求被提交了

275
00:11:33,920 --> 00:11:36,360
由请求方，Stanley。

276
00:11:36,360 --> 00:11:37,240
我们做一件事。

277
00:11:37,240 --> 00:11:40,120
我们重命名工作区名称。

278
00:11:40,120 --> 00:11:42,800
因为现在采购请求需要一个参考。

279
00:11:42,800 --> 00:11:45,760
因为如果我想与采购部门谈论

280
00:11:45,760 --> 00:11:48,640
某个请求，对吧，我需要一个参考

281
00:11:48,640 --> 00:11:49,640
号码。

282
00:11:49,640 --> 00:11:52,120
所以我生成了一个参考号码，

283
00:11:52,120 --> 00:11:55,680
这是一个运行序列，并相应地制作工作区名称

284
00:11:55,680 --> 00:11:57,000
。

285
00:11:57,000 --> 00:12:02,520
另外，我们也给一个过滤器。

286
00:12:02,520 --> 00:12:07,040
我的意思是，我们使用工作区标签来说，

287
00:12:07,040 --> 00:12:10,440
我想要过滤采购请求。

288
00:12:10,440 --> 00:12:14,360
我也可以选择日期，MX32。

289
00:12:14,360 --> 00:12:17,680
然后这就是我们现在构建的方式。

290
00:12:17,760 --> 00:12:19,840
显然，有更好的方法来做。

291
00:12:19,840 --> 00:12:22,760
但这就是他们如何识别并快速

292
00:12:22,760 --> 00:12:26,640
到达他们的请求。

293
00:12:26,640 --> 00:12:27,240
两个标签。

294
00:12:27,240 --> 00:12:31,320
一个是分类PR或PO或GRN。

295
00:12:31,320 --> 00:12:34,280
第二个是参考号码。

296
00:12:34,280 --> 00:12:36,240
参考号码，Stanley。

297
00:12:36,240 --> 00:12:38,240
所以这样，他们可以快速到达他们的PO。

298
00:12:38,240 --> 00:12:38,760
是的。

299
00:12:38,760 --> 00:12:40,360
类别列表号码在哪里？

300
00:12:40,360 --> 00:12:41,360
这些是我们的标签。

301
00:12:41,360 --> 00:12:45,720
我们实际上生成它，并将其存储在标签中。

302
00:12:45,720 --> 00:12:46,240
我们生成。

303
00:12:46,240 --> 00:12:47,320
不是作为功能，对吧？

304
00:12:47,360 --> 00:12:48,880
不，不。

305
00:12:48,880 --> 00:12:50,920
它还没有到达SAP。

306
00:12:50,920 --> 00:12:53,760
这只是一个采购请求申请。

307
00:12:53,760 --> 00:12:57,120
它仍然处于草稿模式，等待决定。

308
00:12:57,120 --> 00:12:59,400
现在有人必须决定是否继续前进。

309
00:12:59,400 --> 00:13:01,000
然后它去SAP。

310
00:13:01,000 --> 00:13:02,160
它仍然在我们这里。

311
00:13:02,160 --> 00:13:02,680
是的。

312
00:13:02,680 --> 00:13:05,480
所以给出一些唯一的号码来稍后识别，

313
00:13:05,480 --> 00:13:07,480
甚至用于对账。

314
00:13:07,480 --> 00:13:10,080
如果出了问题，它没有工作。

315
00:13:10,080 --> 00:13:10,760
是的。

316
00:13:10,760 --> 00:13:13,000
它没有发布到SAP。

317
00:13:13,000 --> 00:13:14,400
我认为我们仍然需要一些唯一的。

318
00:13:14,400 --> 00:13:16,200
我们可以创建一个基金。

319
00:13:16,200 --> 00:13:18,600
我们不能创建基金，你的意思是？

320
00:13:18,600 --> 00:13:19,240
我们可以。

321
00:13:19,240 --> 00:13:20,880
我们可以从一开始就做。

322
00:13:20,880 --> 00:13:24,200
这就是Vars博士说的。

323
00:13:24,200 --> 00:13:25,680
因为我必须触发某些东西，

324
00:13:25,680 --> 00:13:27,400
我把它放在触发逻辑中，Stanley。

325
00:13:27,400 --> 00:13:30,640
否则，你可以在工作区时分配它。

326
00:13:30,640 --> 00:13:34,240
有一件事是在流程模板上。

327
00:13:34,240 --> 00:13:37,040
当工作区命名时，我们使用了那个DDR8。

328
00:13:37,040 --> 00:13:37,560
是的。

329
00:13:37,560 --> 00:13:40,240
这是有一些序列号的某种方式，

330
00:13:40,240 --> 00:13:41,440
要附加的东西。

331
00:13:41,440 --> 00:13:42,000
是的。

332
00:13:42,000 --> 00:13:43,280
然后它会生成。

333
00:13:44,240 --> 00:13:45,880
这只是为了参考，Stanley。

334
00:13:45,880 --> 00:13:47,240
否则，他们会感到困惑。

335
00:13:47,240 --> 00:13:49,080
这么多工作流程，这么多事情。

336
00:13:49,080 --> 00:13:50,680
所以他们会感到困惑。

337
00:13:50,680 --> 00:13:52,720
是的。

338
00:13:52,720 --> 00:13:54,320
这是一些日期和时间戳，某些东西。

339
00:13:54,320 --> 00:13:56,600
也许我们可以使用逻辑来使其唯一。

340
00:13:56,600 --> 00:13:58,520
哦，是的，那很好。

341
00:13:58,520 --> 00:14:00,040
运行号码应该没问题。

342
00:14:00,040 --> 00:14:03,000
号码，唯一的问题是，他们需要一个用于PR，一个用于PO，

343
00:14:03,000 --> 00:14:04,480
一个用于PRN。

344
00:14:04,480 --> 00:14:07,560
这不是可以基于过程制作的，

345
00:14:07,560 --> 00:14:09,320
基于模板。

346
00:14:09,320 --> 00:14:11,800
这就是我在想的。

347
00:14:11,800 --> 00:14:13,720
我们需要一种方法来命名它。

348
00:14:13,720 --> 00:14:15,400
现在，我会审查Stanley。

349
00:14:15,400 --> 00:14:19,160
我的意思是，我会批准它作为采购申请，对吧？

350
00:14:19,160 --> 00:14:21,560
所以你会看到它通过。

351
00:14:25,000 --> 00:14:27,240
所以有人提出了它，有人批准了它。

352
00:14:27,240 --> 00:14:27,720
是的。

353
00:14:27,720 --> 00:14:29,240
那现在会去SAP。

354
00:14:29,240 --> 00:14:30,200
SAP，是的。

355
00:14:30,200 --> 00:14:31,200
是的。

356
00:14:31,200 --> 00:14:32,160
它如何去SAP？

357
00:14:36,440 --> 00:14:38,280
好吧，这是SAP，对吧？

358
00:14:38,280 --> 00:14:41,560
任何人安装B1，当他们更新AI服务

359
00:14:41,560 --> 00:14:42,400
你在谈论的，对吧？

360
00:14:42,400 --> 00:14:43,080
是的，是的，是的。

361
00:14:43,080 --> 00:14:45,240
他们都有非常相似的界面，对吧？

362
00:14:45,240 --> 00:14:47,320
我们可以构建一个连接器来工作。

363
00:14:47,320 --> 00:14:48,000
是的，是的，是的。

364
00:14:48,000 --> 00:14:49,360
我们也会讨论那个。

365
00:14:49,360 --> 00:14:50,520
它应该工作。

366
00:14:50,520 --> 00:14:51,880
我会给你展示源代码。

367
00:14:51,880 --> 00:14:53,960
你有那个源代码。

368
00:14:53,960 --> 00:14:56,400
所以基本上，你可以暴露所有对象

369
00:14:56,400 --> 00:14:59,240
我们想要作为获取API。

370
00:14:59,240 --> 00:15:00,680
他准备这样做。

371
00:15:00,680 --> 00:15:01,920
实际上，他在说。

372
00:15:01,920 --> 00:15:04,640
这现在只能在内部安装，是吗？

373
00:15:04,640 --> 00:15:05,960
没有云版本。

374
00:15:05,960 --> 00:15:06,640
没有云。

375
00:15:06,640 --> 00:15:09,360
SAP B1根本不是云。

376
00:15:09,360 --> 00:15:10,200
是的。

377
00:15:10,200 --> 00:15:20,680
所以在那个表中，我们刚刚看到了视图列表。

378
00:15:20,680 --> 00:15:25,160
那是需要从SAP来的数据。

379
00:15:25,160 --> 00:15:26,560
多个下拉菜单，对吧？

380
00:15:26,560 --> 00:15:27,880
是的，多个下拉菜单。

381
00:15:27,880 --> 00:15:31,440
那些下拉菜单可能彼此有关系。

382
00:15:31,440 --> 00:15:33,080
依赖性非常，非常常见。

383
00:15:33,080 --> 00:15:34,920
是的，非常常见。

384
00:15:34,920 --> 00:15:39,920
那个下拉菜单，即使下拉菜单显示

385
00:15:39,920 --> 00:15:43,200
可能是一个表格。

386
00:15:43,200 --> 00:15:43,960
可能。

387
00:15:43,960 --> 00:15:44,440
对。

388
00:15:44,440 --> 00:15:45,640
多于一个值，是的。

389
00:15:45,640 --> 00:15:46,600
多于一个值。

390
00:15:46,600 --> 00:15:47,440
像数据表。

391
00:15:47,440 --> 00:15:53,440
是的，数据表，然后你选择使用哪一个。

392
00:15:53,440 --> 00:15:58,280
数据表，如果我们想要，我们也可以显示更多作为文本信息。

393
00:15:58,280 --> 00:16:00,680
是的，然后你只选择一个。

394
00:16:00,680 --> 00:16:01,680
无论如何，只有一个。

395
00:16:01,680 --> 00:16:02,840
是的，然后你选择一个。

396
00:16:02,840 --> 00:16:03,480
是的。

397
00:16:03,480 --> 00:16:05,680
但你可能从SAP获得多于一个数据

398
00:16:05,680 --> 00:16:07,200
对于同一个角色。

399
00:16:07,200 --> 00:16:07,800
是的。

400
00:16:07,800 --> 00:16:09,760
像Palav Singh，是的。

401
00:16:10,600 --> 00:16:13,080
然后你可以获取它并显示它。

402
00:16:13,080 --> 00:16:13,600
是的。

403
00:16:13,600 --> 00:16:17,520
你可以获得1000个左右。

404
00:16:17,520 --> 00:16:18,040
对。

405
00:16:18,040 --> 00:16:21,360
下拉菜单中的一个空间。

406
00:16:21,360 --> 00:16:22,680
那可能仍然没问题。

407
00:16:22,680 --> 00:16:26,360
所以这里是采购请求，它已经来到SAP。

408
00:16:26,360 --> 00:16:30,080
如你所知，我们选择了30和40，两个项目。

409
00:16:30,080 --> 00:16:32,840
建议的供应商也来了。

410
00:16:32,840 --> 00:16:35,880
所以有时他们可能也不给供应商。

411
00:16:35,880 --> 00:16:38,320
这取决于他们，但这个组织

412
00:16:38,320 --> 00:16:40,280
也想要建议的供应商。

413
00:16:40,280 --> 00:16:42,160
因为这完全取决于他们如何设置。

414
00:16:42,160 --> 00:16:43,280
正确，正确，正确。

415
00:16:43,280 --> 00:16:44,640
是的，关于那个一点。

416
00:16:44,640 --> 00:16:47,680
但是是的，供应商，他们有一些地方

417
00:16:47,680 --> 00:16:49,360
向你展示供应商。

418
00:16:49,360 --> 00:16:49,960
是的，是的，是的。

419
00:16:49,960 --> 00:16:52,520
这就是我们在第一个链接中做的。

420
00:16:52,520 --> 00:16:55,200
是的，在下拉菜单中，在下拉菜单中，是的。

421
00:16:55,200 --> 00:16:56,440
但那是，是的。

422
00:16:56,440 --> 00:16:57,520
对不起，Palav。

423
00:16:57,520 --> 00:16:58,760
也许你可以完成。

424
00:16:58,760 --> 00:17:02,720
是的，所以UOM，一切都过来了，对吧？

425
00:17:02,720 --> 00:17:03,320
现在。

426
00:17:03,320 --> 00:17:05,600
You did not do anything to split the vendor out, right?

427
00:17:05,600 --> 00:17:06,360
No, no, no.

428
00:17:06,360 --> 00:17:08,200
You just send the item number.

429
00:17:08,320 --> 00:17:12,160
Oh, we did split that V006 vendor code.

430
00:17:12,160 --> 00:17:13,480
They did some work on the way.

431
00:17:13,480 --> 00:17:14,320
Yeah, yeah, yeah.

432
00:17:14,320 --> 00:17:17,920
On the way back, we had to separate it out.

433
00:17:17,920 --> 00:17:21,000
OK, so now from this time, let's say

434
00:17:21,000 --> 00:17:24,400
I want to put this into a purchase order.

435
00:17:24,400 --> 00:17:26,600
As a procurement person, I'll directly

436
00:17:26,600 --> 00:17:29,400
create a purchase order like this.

437
00:17:29,400 --> 00:17:34,480
The thing is, see, why I say it was a suggested vendor is.

438
00:17:34,480 --> 00:17:35,760
Now they will put the real.

439
00:17:35,760 --> 00:17:37,600
Now they'll put the real vendor standing.

440
00:17:37,600 --> 00:17:40,200
Because they may ask for a different vendor

441
00:17:40,200 --> 00:17:42,080
for a different line item, right?

442
00:17:42,080 --> 00:17:44,880
But the procurement department fellow will decide.

443
00:17:44,880 --> 00:17:46,720
Now he can also delete this item,

444
00:17:46,720 --> 00:17:48,960
saying I'll create another purchase order.

445
00:17:48,960 --> 00:17:51,160
I don't want to mix up the purchase order.

446
00:17:51,160 --> 00:17:53,520
So that is all a procurement decision.

447
00:17:53,520 --> 00:17:55,480
But we don't need to be bothered with all that.

448
00:17:55,480 --> 00:17:58,640
Basically, they will do all this in SAP, right?

449
00:17:58,640 --> 00:18:00,480
So they do all this.

450
00:18:00,480 --> 00:18:02,400
And the warehouse is also an important thing.

451
00:18:02,400 --> 00:18:06,120
Oh, that's the other thing that I wanted to tell.

452
00:18:06,520 --> 00:18:08,520
In the user profile, we have configured

453
00:18:08,520 --> 00:18:10,520
the warehouse to which that person belongs

454
00:18:10,520 --> 00:18:13,000
to, the department code.

455
00:18:13,000 --> 00:18:14,920
The reason that is important is that it

456
00:18:14,920 --> 00:18:18,760
restricts your visibility in SAP as to what you see.

457
00:18:18,760 --> 00:18:21,120
Purchase orders, what purchase orders

458
00:18:21,120 --> 00:18:23,720
you can see for your goods receiving.

459
00:18:23,720 --> 00:18:27,080
It doesn't matter for the other things.

460
00:18:27,080 --> 00:18:29,600
But only for the goods received.

461
00:18:29,600 --> 00:18:32,320
In one purchase order, let's say there are five items.

462
00:18:32,320 --> 00:18:35,160
Each item in the warehouse may be marked.

463
00:18:35,160 --> 00:18:37,200
So if three are in one warehouse,

464
00:18:37,200 --> 00:18:38,960
other two are in another, that warehouse,

465
00:18:38,960 --> 00:18:40,800
one guy should not see the other two.

466
00:18:40,800 --> 00:18:41,800
He can only see those three.

467
00:18:41,800 --> 00:18:42,640
Correct, correct.

468
00:18:42,640 --> 00:18:45,080
But that you can do in the fetch, get API.

469
00:18:45,080 --> 00:18:45,920
In the get API, you can do that.

470
00:18:45,920 --> 00:18:48,040
In the data capture, we should not be worried.

471
00:18:48,040 --> 00:18:51,480
Whoever is booking that GRN should be very clear

472
00:18:51,480 --> 00:18:52,800
from which warehouse he's belonged to.

473
00:18:52,800 --> 00:18:53,640
Correct, correct, correct.

474
00:18:53,640 --> 00:18:54,840
So his visibility can be controlled.

475
00:18:54,840 --> 00:18:55,680
Correct, yeah.

476
00:18:55,680 --> 00:18:56,520
So the user...

477
00:18:56,520 --> 00:18:58,680
Which is our logic, that is an SAP logic

478
00:18:58,680 --> 00:19:01,040
when we get to filter out, right?

479
00:19:01,040 --> 00:19:04,400
You can do that through a complex query,

480
00:19:04,400 --> 00:19:06,520
meaning you have to look at the...

481
00:19:06,520 --> 00:19:08,360
Also we'll extract that out, right?

482
00:19:08,360 --> 00:19:09,480
What connector we are building,

483
00:19:09,480 --> 00:19:10,720
all this logic should go inside.

484
00:19:10,720 --> 00:19:11,560
You can.

485
00:19:11,560 --> 00:19:12,960
That's a decision you have to take.

486
00:19:12,960 --> 00:19:14,920
I mean, where you want to put that logic.

487
00:19:14,920 --> 00:19:16,800
Because that is nothing to do with our forms as such, right?

488
00:19:16,800 --> 00:19:18,280
Yeah, I mean, yes.

489
00:19:18,280 --> 00:19:21,360
It is only what items I'm allowed to select.

490
00:19:21,360 --> 00:19:24,120
That logic, you can decide where it is going.

491
00:19:24,120 --> 00:19:26,000
Right now, we have put it in the form,

492
00:19:26,000 --> 00:19:27,800
in the user display ID.

493
00:19:27,800 --> 00:19:30,160
Yeah, user ID and used it like that.

494
00:19:30,160 --> 00:19:31,000
Yeah.

495
00:19:31,960 --> 00:19:32,800
Correct.

496
00:19:32,800 --> 00:19:34,640
Connector should have that.

497
00:19:34,640 --> 00:19:35,480
Yeah.

498
00:19:35,480 --> 00:19:37,560
That Apple should have the logic.

499
00:19:37,560 --> 00:19:38,880
What I would suggest, Stanley,

500
00:19:38,880 --> 00:19:41,360
is that each of them has a default implementation

501
00:19:41,360 --> 00:19:43,840
of the API, right?

502
00:19:43,840 --> 00:19:46,560
With a certain query, SQL query, right?

503
00:19:46,560 --> 00:19:49,880
If I want, our team can change that SQL query

504
00:19:49,880 --> 00:19:51,920
for a particular org, if required.

505
00:19:51,920 --> 00:19:52,760
Something like that.

506
00:19:52,760 --> 00:19:55,360
Most likely, we'll build it through a framework,

507
00:19:55,360 --> 00:19:57,000
integration framework, kind of an app.

508
00:19:57,000 --> 00:19:57,840
Okay, okay.

509
00:19:57,840 --> 00:19:58,680
So in that, there should be a way

510
00:19:59,600 --> 00:20:01,320
to customize your logic.

511
00:20:01,320 --> 00:20:02,160
Okay, okay.

512
00:20:02,160 --> 00:20:03,000
Okay.

513
00:20:03,000 --> 00:20:03,840
Fine, fine.

514
00:20:03,840 --> 00:20:04,680
I mean, I'm just saying.

515
00:20:04,680 --> 00:20:05,520
Yeah, yeah, you're right.

516
00:20:05,520 --> 00:20:06,680
Yeah, yeah, yeah.

517
00:20:06,680 --> 00:20:09,440
Maybe they can select where else they belong to,

518
00:20:09,440 --> 00:20:11,560
and then that will show the items, whatever.

519
00:20:11,560 --> 00:20:12,880
Where they want to receive.

520
00:20:12,880 --> 00:20:14,800
But whether it is acceptable or not,

521
00:20:14,800 --> 00:20:16,480
we need to check with the...

522
00:20:16,480 --> 00:20:17,960
I would rather, all those,

523
00:20:17,960 --> 00:20:19,800
we can keep it more to coding for now, right?

524
00:20:19,800 --> 00:20:20,640
Yeah, yeah, yeah, yeah.

525
00:20:20,640 --> 00:20:21,640
That could be a lot of scenarios.

526
00:20:21,640 --> 00:20:22,920
Yeah, yeah.

527
00:20:22,920 --> 00:20:23,760
So, okay.

528
00:20:23,760 --> 00:20:28,440
Now, I have selected the vendor.

529
00:20:28,440 --> 00:20:30,800
I have to select the unit price on the PO.

530
00:20:30,800 --> 00:20:34,400
This will be something I've agreed with the vendor, right?

531
00:20:34,400 --> 00:20:36,680
Usually, in more complex SAP systems,

532
00:20:36,680 --> 00:20:38,680
this is all taken from a contract,

533
00:20:38,680 --> 00:20:40,440
or it'll default from a contract,

534
00:20:40,440 --> 00:20:44,280
or some price determination will happen from another system.

535
00:20:44,280 --> 00:20:46,120
But the PO creation will happen here, right?

536
00:20:46,120 --> 00:20:46,960
Correct, correct.

537
00:20:46,960 --> 00:20:48,120
That, they don't do it outside.

538
00:20:48,120 --> 00:20:48,960
No, no, no.

539
00:20:48,960 --> 00:20:51,200
They will have to do it in SAP.

540
00:20:51,200 --> 00:20:53,560
So, we don't need to worry about that logic,

541
00:20:53,560 --> 00:20:57,040
because even if they put that complex logic,

542
00:20:57,040 --> 00:21:00,280
we just have to deal with the purchase order onwards.

543
00:21:00,280 --> 00:21:02,360
So, I'll just say, add a new here.

544
00:21:02,360 --> 00:21:05,200
So, as you know, Sally, there are two items.

545
00:21:05,200 --> 00:21:06,840
He's adding one more right now?

546
00:21:06,840 --> 00:21:07,680
No, no.

547
00:21:07,680 --> 00:21:08,520
Just two items.

548
00:21:08,520 --> 00:21:09,360
That's it.

549
00:21:09,360 --> 00:21:11,080
I could have added more items also

550
00:21:11,080 --> 00:21:13,160
from another purchase request.

551
00:21:13,160 --> 00:21:16,400
So, that and all is possible.

552
00:21:16,400 --> 00:21:18,040
Now, we have done that, right?

553
00:21:18,040 --> 00:21:18,880
That is done.

554
00:21:19,720 --> 00:21:22,400
Now, if you look at the user-defined fields,

555
00:21:22,400 --> 00:21:25,280
what we did was, we, to keep the synchronization,

556
00:21:25,280 --> 00:21:29,360
you will see that, to say whether this is posted in Moxo,

557
00:21:29,360 --> 00:21:31,080
we say it's no by default.

558
00:21:31,080 --> 00:21:34,160
So, as soon as he gets a return, successful return,

559
00:21:35,200 --> 00:21:37,480
he makes it yes here.

560
00:21:37,480 --> 00:21:41,400
So, this is the EXE we need to run for that.

561
00:21:41,400 --> 00:21:42,240
It'll sync now.

562
00:21:42,240 --> 00:21:43,080
It'll sync now.

563
00:21:43,080 --> 00:21:44,800
It'll look for all non-posted Moxos.

564
00:21:44,800 --> 00:21:45,640
Exactly.

565
00:21:45,640 --> 00:21:50,640
It'll look for all non-posted POs and do that, okay?

566
00:21:51,640 --> 00:21:54,000
Now, I'll refresh this record.

567
00:21:54,000 --> 00:21:55,800
You'll see that it's become yes here.

568
00:21:55,800 --> 00:21:57,520
It's posted to Moxo.

569
00:21:57,520 --> 00:21:59,680
If you note the PO number, it says 59.

570
00:21:59,680 --> 00:22:02,320
This is the reference number with which we'll search.

571
00:22:02,320 --> 00:22:03,560
SAP number.

572
00:22:03,560 --> 00:22:05,200
What is that ID, Bala?

573
00:22:05,200 --> 00:22:06,040
Which one?

574
00:22:06,040 --> 00:22:06,880
ID Moxo.

575
00:22:08,000 --> 00:22:10,480
ID Moxo is nothing but in his logs,

576
00:22:10,480 --> 00:22:11,640
he's printing all this.

577
00:22:11,640 --> 00:22:12,480
Some ID.

578
00:22:12,480 --> 00:22:15,360
Yeah, this is a ID that you can correlate

579
00:22:15,360 --> 00:22:17,040
in the logs on the SAP side.

580
00:22:17,040 --> 00:22:17,880
Just to see the integration.

581
00:22:17,880 --> 00:22:21,200
Yeah, on the SAP side, you can see the logs and all that.

582
00:22:21,200 --> 00:22:23,280
So, this is some ID they generate

583
00:22:23,280 --> 00:22:24,920
and we are just storing it.

584
00:22:24,920 --> 00:22:26,240
We may not show it.

585
00:22:26,240 --> 00:22:27,080
I mean, it's up to us.

586
00:22:27,080 --> 00:22:28,560
No, it's good for installation.

587
00:22:28,560 --> 00:22:30,040
Correct, yeah.

588
00:22:30,040 --> 00:22:31,120
So, this part is done.

589
00:22:31,120 --> 00:22:36,120
Now, the PO has come to the vendor, right?

590
00:22:36,200 --> 00:22:39,840
It also comes to the vendor for acknowledging.

591
00:22:43,720 --> 00:22:45,600
So, here what we did, Stanley,

592
00:22:45,600 --> 00:22:48,800
by default that middleware, what it does is

593
00:22:48,800 --> 00:22:50,920
it creates a vendor communication binder

594
00:22:50,920 --> 00:22:53,240
if it is not existing, okay?

595
00:22:54,080 --> 00:22:56,040
It goes by a binder tag, okay?

596
00:22:56,040 --> 00:22:58,160
So, binder tag, it says, okay,

597
00:22:58,160 --> 00:23:01,640
is there already a folder for communications?

598
00:23:01,640 --> 00:23:04,760
If it is there, it'll just put that PO in that folder.

599
00:23:04,760 --> 00:23:06,520
Yeah, so, put a new flow down there.

600
00:23:06,520 --> 00:23:07,360
Put it in inbox.

601
00:23:07,360 --> 00:23:08,320
Put a new flow.

602
00:23:08,320 --> 00:23:09,520
No, we just put a new flow.

603
00:23:09,520 --> 00:23:10,520
Not required, Stanley.

604
00:23:10,520 --> 00:23:12,400
There's only a action.

605
00:23:12,400 --> 00:23:14,520
You can put it on inbox also.

606
00:23:14,520 --> 00:23:17,240
So, what I can say is in the new flow,

607
00:23:17,240 --> 00:23:19,040
nothing else, but it's only for.

608
00:23:19,040 --> 00:23:19,880
Sure, sure.

609
00:23:19,880 --> 00:23:22,240
Then, because there's no matter what,

610
00:23:22,880 --> 00:23:24,840
only if you say one action or multiple.

611
00:23:24,840 --> 00:23:25,880
Yeah, yeah, that's fine, too.

612
00:23:25,880 --> 00:23:26,720
Yeah, yeah, yeah.

613
00:23:26,720 --> 00:23:28,040
That can be that, yeah, yeah.

614
00:23:28,040 --> 00:23:28,880
It's the one-step.

615
00:23:28,880 --> 00:23:29,720
Single action.

616
00:23:29,720 --> 00:23:30,560
One-step flow.

617
00:23:30,560 --> 00:23:32,440
One-step flow, yeah, yeah.

618
00:23:32,440 --> 00:23:36,160
So, now he has to review and acknowledge the PO.

619
00:23:36,160 --> 00:23:38,800
So, if he, you know, we put this restriction

620
00:23:38,800 --> 00:23:39,920
saying they have to review.

621
00:23:39,920 --> 00:23:41,560
So, they're happy saying, oh, yeah,

622
00:23:41,560 --> 00:23:43,200
now we are ensuring that.

623
00:23:43,200 --> 00:23:44,040
He's reading it.

624
00:23:44,040 --> 00:23:44,860
He's reading that.

625
00:23:44,860 --> 00:23:47,400
So, we created just an HTML document here

626
00:23:47,400 --> 00:23:50,200
based on the PO information we received from SAP.

627
00:23:51,160 --> 00:23:53,960
That means that it gets equally.

628
00:23:53,960 --> 00:23:55,600
No, no, no.

629
00:23:55,600 --> 00:23:58,120
Our middleware has created this.

630
00:23:58,120 --> 00:24:01,040
From the SAP data, we kind of generated a.

631
00:24:01,040 --> 00:24:03,320
It can be an acknowledgement object also, Stanley.

632
00:24:03,320 --> 00:24:06,160
I've just made it a little more nice.

633
00:24:06,160 --> 00:24:07,760
It can be a PDF.

634
00:24:07,760 --> 00:24:08,600
It can be a PDF.

635
00:24:08,600 --> 00:24:10,400
Nice PDF.

636
00:24:10,400 --> 00:24:14,960
But then SAP went on and generated this PDF.

637
00:24:14,960 --> 00:24:17,240
No, no, not B1.

638
00:24:17,240 --> 00:24:19,080
There are versions of SAP which will

639
00:24:19,120 --> 00:24:21,200
generate the document also.

640
00:24:21,200 --> 00:24:24,800
But for that, I mean, you can also build this logic

641
00:24:24,800 --> 00:24:26,160
in the connector if you want to.

642
00:24:26,160 --> 00:24:27,600
You can send the PDF from there.

643
00:24:27,600 --> 00:24:30,240
Of course, we need a very strong connector to support it.

644
00:24:30,240 --> 00:24:32,240
So, I mean, that's our decision.

645
00:24:32,240 --> 00:24:33,800
Yeah, yeah, we'll further abstract it.

646
00:24:33,800 --> 00:24:35,320
We'll only make changes.

647
00:24:35,320 --> 00:24:37,080
Because, Stanley, you have multiple item.

648
00:24:37,080 --> 00:24:38,760
This could be long also.

649
00:24:38,760 --> 00:24:40,280
It could have 10 items also.

650
00:24:40,280 --> 00:24:42,760
So, you can't really put it in a card, actually,

651
00:24:42,760 --> 00:24:43,680
acknowledgement card.

652
00:24:43,680 --> 00:24:47,120
You may want to show it as a proper table and all that.

653
00:24:47,160 --> 00:24:52,800
So, now I acknowledge this and confirm.

654
00:24:52,800 --> 00:24:56,040
So, this is done right.

655
00:24:56,040 --> 00:24:58,200
Now, when I'm shipping the goods,

656
00:24:58,200 --> 00:25:03,560
I can come and raise an ASN, Advanced Shipment Notice.

657
00:25:03,560 --> 00:25:07,120
So, how do they acknowledge it?

658
00:25:07,120 --> 00:25:09,840
Acknowledgement, not in B1, Stanley.

659
00:25:09,840 --> 00:25:12,160
There's no place to keep it.

660
00:25:12,160 --> 00:25:14,200
They may do one thing, Stanley.

661
00:25:14,200 --> 00:25:17,240
They may create a user-defined field

662
00:25:17,240 --> 00:25:19,480
called Acknowledgement Received from Supplier.

663
00:25:19,480 --> 00:25:21,040
They can do that.

664
00:25:21,040 --> 00:25:22,720
Then store it there.

665
00:25:22,720 --> 00:25:23,600
We can send it back.

666
00:25:23,600 --> 00:25:24,120
Yeah.

667
00:25:24,120 --> 00:25:26,000
People, you know, they change it a bit

668
00:25:26,000 --> 00:25:27,480
based on their requirements.

669
00:25:27,480 --> 00:25:29,200
The acknowledgement process may not

670
00:25:29,200 --> 00:25:30,480
be mandatory for some business also.

671
00:25:30,480 --> 00:25:30,960
Correct.

672
00:25:30,960 --> 00:25:34,160
They may not want acknowledgement also, some of them.

673
00:25:34,160 --> 00:25:36,880
I mean, like, they're OK without acknowledgement also.

674
00:25:36,880 --> 00:25:39,680
But just that this improves our value.

675
00:25:39,680 --> 00:25:41,480
And that could be an acknowledgement on email

676
00:25:41,480 --> 00:25:42,440
also, currently, for it.

677
00:25:42,440 --> 00:25:43,480
Correct, correct.

678
00:25:43,480 --> 00:25:46,160
Now, you see, I have flattened the purchase order.

679
00:25:46,160 --> 00:25:50,240
I'm not showing a purchase order separately and items

680
00:25:50,240 --> 00:25:50,800
separately.

681
00:25:50,800 --> 00:25:54,880
I have flattened all the details into one single line.

682
00:25:54,880 --> 00:25:56,200
Yeah, here, a lot of it.

683
00:25:56,200 --> 00:25:56,840
Yeah.

684
00:25:56,840 --> 00:25:58,640
So now, you opened it quickly, right, Bala?

685
00:25:58,640 --> 00:25:59,160
Yeah, yeah.

686
00:25:59,160 --> 00:26:02,040
So this is a vendor portal interaction, right?

687
00:26:02,040 --> 00:26:02,600
Correct.

688
00:26:02,600 --> 00:26:03,920
So now, vendor is coming.

689
00:26:03,920 --> 00:26:05,360
He is confirming.

690
00:26:05,360 --> 00:26:06,880
He has a bunch of orders.

691
00:26:06,880 --> 00:26:08,320
And he's saying, I'm shipping this now.

692
00:26:08,320 --> 00:26:08,800
Correct.

693
00:26:08,800 --> 00:26:11,440
So he's now raising a shipment notification

694
00:26:11,440 --> 00:26:12,240
to the business.

695
00:26:12,240 --> 00:26:14,760
Correct.

696
00:26:14,760 --> 00:26:18,480
Now, to make it easy, we have made the UI slightly different.

697
00:26:18,480 --> 00:26:19,960
OK, there's some bugs here.

698
00:26:19,960 --> 00:26:21,160
And I can sell it one item.

699
00:26:21,160 --> 00:26:22,640
This is how old we do it.

700
00:26:22,640 --> 00:26:25,680
Yeah, this is how old we do it.

701
00:26:25,680 --> 00:26:30,320
This one is, I made it as flexible as possible by,

702
00:26:30,320 --> 00:26:31,960
I mean, I'll talk about it later.

703
00:26:31,960 --> 00:26:33,720
Hopefully, we can get rid of all this.

704
00:26:33,720 --> 00:26:36,240
You can get rid of the SAP.

705
00:26:36,240 --> 00:26:37,760
In a table stand, in a post box.

706
00:26:37,760 --> 00:26:41,160
Right now, Bala is also storing everything from SAP locally.

707
00:26:41,160 --> 00:26:43,360
So this data is now in SAP?

708
00:26:43,360 --> 00:26:45,000
This data is in SAP already, right?

709
00:26:45,000 --> 00:26:48,080
This has come from SAP, the purchase order data.

710
00:26:48,080 --> 00:26:52,920
Now, if I create a ASN, it will be here only.

711
00:26:52,920 --> 00:26:54,680
It will go to the buyer.

712
00:26:54,680 --> 00:26:57,160
But you can also put user-defined fields

713
00:26:57,160 --> 00:26:59,200
in B1, where it can go as saying,

714
00:26:59,200 --> 00:27:00,320
it received an advancement.

715
00:27:00,320 --> 00:27:03,120
So in your app, you just store the new data?

716
00:27:03,120 --> 00:27:03,920
Yeah, yeah.

717
00:27:03,920 --> 00:27:04,680
In a post-release.

718
00:27:04,680 --> 00:27:06,920
In reality, it can be fetched.

719
00:27:06,920 --> 00:27:08,880
If you have a connector, you can live-fetch.

720
00:27:08,880 --> 00:27:10,480
Yeah, you can live-fetch this, Stanley.

721
00:27:10,480 --> 00:27:12,200
You don't need this middle layer, right?

722
00:27:12,200 --> 00:27:16,880
Because if I have a connector, I mean, that live-fetch, right?

723
00:27:16,880 --> 00:27:19,200
Live-fetch SAP.

724
00:27:19,200 --> 00:27:19,700
Yeah.

725
00:27:19,700 --> 00:27:23,000
So that means SAP has that SM data.

726
00:27:23,000 --> 00:27:23,680
Not ASN.

727
00:27:23,680 --> 00:27:24,640
They have the PO data.

728
00:27:24,640 --> 00:27:26,200
Right now, I'm selecting the PO.

729
00:27:26,200 --> 00:27:28,680
So this is PO data for that vendor?

730
00:27:28,680 --> 00:27:29,960
For that vendor.

731
00:27:29,960 --> 00:27:32,080
Because this is the item I'm shipping.

732
00:27:32,080 --> 00:27:33,080
There's a bug, actually.

733
00:27:33,080 --> 00:27:34,600
It's selecting all the data.

734
00:27:34,600 --> 00:27:36,560
When I do a search, it's selecting anything.

735
00:27:36,560 --> 00:27:41,160
So you're talking about now your vendors review.

736
00:27:41,160 --> 00:27:42,640
Yes.

737
00:27:42,640 --> 00:27:47,360
Then that means in the future, our vendor

738
00:27:47,360 --> 00:27:53,120
can tick off for some of the ASN process.

739
00:27:53,120 --> 00:27:53,880
Yeah.

740
00:27:53,880 --> 00:27:56,520
And through starting, that's easy.

741
00:27:56,520 --> 00:27:59,640
When they tick off that, they need to pick up.

742
00:27:59,640 --> 00:28:05,840
Then you go to the SAP to pick up what list of the-

743
00:28:05,840 --> 00:28:06,960
Purchase orders for me.

744
00:28:06,960 --> 00:28:08,760
PO, they are involved.

745
00:28:08,760 --> 00:28:09,640
Yes, yes.

746
00:28:09,640 --> 00:28:12,320
Today, this is totally black box for them.

747
00:28:12,320 --> 00:28:14,560
Because vendors don't have any access to SAP.

748
00:28:14,560 --> 00:28:17,080
So this, we have been bringing the bridge.

749
00:28:17,080 --> 00:28:19,520
So business may find this very valuable.

750
00:28:19,520 --> 00:28:20,200
Correct, correct.

751
00:28:20,200 --> 00:28:22,960
But will vendors really be so disciplined?

752
00:28:22,960 --> 00:28:24,400
No, this is optional.

753
00:28:24,400 --> 00:28:26,840
So in many cases, they're saying, no, we

754
00:28:26,840 --> 00:28:28,080
don't need this, also.

755
00:28:28,080 --> 00:28:30,400
They'll directly go to goods received.

756
00:28:30,400 --> 00:28:32,200
See, I'm saying we show all this.

757
00:28:32,200 --> 00:28:34,440
From business side, the operations may be limited.

758
00:28:34,440 --> 00:28:35,760
From business side, it is optional.

759
00:28:35,760 --> 00:28:36,800
If they want, they can.

760
00:28:36,800 --> 00:28:37,480
Correct, correct.

761
00:28:37,480 --> 00:28:39,080
But we are just showing all the options.

762
00:28:39,080 --> 00:28:40,080
Yeah, options are there.

763
00:28:40,080 --> 00:28:42,600
So the vendor may-

764
00:28:42,600 --> 00:28:46,200
But this one, we have to be really careful, in the sense

765
00:28:46,200 --> 00:28:49,080
that vendors can only see his SEOs and all that.

766
00:28:49,080 --> 00:28:50,960
But that you can control in the query.

767
00:28:50,960 --> 00:28:53,720
In the query, you can control in the SAP query, right?

768
00:28:53,720 --> 00:28:55,120
Yeah.

769
00:28:55,120 --> 00:28:56,320
Connector.

770
00:28:56,320 --> 00:28:57,400
Automation and-

771
00:28:57,400 --> 00:28:58,440
Correct, correct, correct.

772
00:28:58,440 --> 00:28:58,920
Got it.

773
00:28:58,920 --> 00:28:59,600
Yeah.

774
00:28:59,600 --> 00:29:01,720
That's where you need to probably enhance the user

775
00:29:01,760 --> 00:29:04,480
profile a little bit, saying, this is the vendor.

776
00:29:04,480 --> 00:29:05,880
I mean, some storing-

777
00:29:05,880 --> 00:29:09,200
We control them through the process.

778
00:29:09,200 --> 00:29:11,520
This is the ASL process.

779
00:29:11,520 --> 00:29:15,160
Then, inside of the automation, you can be there.

780
00:29:15,160 --> 00:29:17,480
Who's the vendor?

781
00:29:17,480 --> 00:29:19,920
Second batch in automation.

782
00:29:19,920 --> 00:29:23,720
That automation, you need to select who are you,

783
00:29:23,720 --> 00:29:25,240
what is this kind of thing.

784
00:29:25,240 --> 00:29:28,480
The one way is to import these two variables, right?

785
00:29:28,480 --> 00:29:31,600
You cannot kick off the ASN without entering the vendor ID.

786
00:29:31,600 --> 00:29:34,160
We can automatically populate the vendor ID even better.

787
00:29:34,160 --> 00:29:35,560
But in the worst case, manually, you

788
00:29:35,560 --> 00:29:37,560
have to enter my vendor ID, which will then

789
00:29:37,560 --> 00:29:38,800
make some kind of-

790
00:29:38,800 --> 00:29:41,920
So what I did is I used the user ID field again

791
00:29:41,920 --> 00:29:46,640
for clients, I mean vendors, to control this, actually.

792
00:29:46,640 --> 00:29:48,440
We have to think about this.

793
00:29:48,440 --> 00:29:49,600
Yeah, I'm just saying what-

794
00:29:49,600 --> 00:29:51,120
Because you cannot misuse also, right?

795
00:29:51,120 --> 00:29:51,800
Put some ID here.

796
00:29:51,800 --> 00:29:58,920
We can do one process, so one file in the vendor.

797
00:29:58,920 --> 00:30:00,520
No, that'll be too much time.

798
00:30:00,520 --> 00:30:03,520
They'll have 100, 400 vendors.

799
00:30:03,520 --> 00:30:06,280
Just forget the query, the 400 vendors.

800
00:30:06,280 --> 00:30:08,280
Startling.

801
00:30:08,280 --> 00:30:11,920
Then, they just send us a sending of each one process

802
00:30:11,920 --> 00:30:15,360
for one vendor, one when they call binding.

803
00:30:15,360 --> 00:30:18,520
Then, they can now switch over to anything else.

804
00:30:18,520 --> 00:30:20,120
So any time they want to start an ASN,

805
00:30:20,120 --> 00:30:21,520
they just keep that thing.

806
00:30:21,520 --> 00:30:24,520
But they have to dispatch 400 emails to let them know.

807
00:30:24,520 --> 00:30:26,000
That's easy.

808
00:30:26,000 --> 00:30:29,440
One time, that one.

809
00:30:29,440 --> 00:30:29,960
Yeah.

810
00:30:29,960 --> 00:30:33,280
Anyway, I'll leave it to you.

811
00:30:33,280 --> 00:30:36,680
These are some details which can be entered in the form also.

812
00:30:36,680 --> 00:30:37,800
Let's say vendor.

813
00:30:37,800 --> 00:30:39,400
Startling is definitely one option.

814
00:30:39,400 --> 00:30:41,000
Then, they don't need to know anything.

815
00:30:41,000 --> 00:30:42,920
So that will be tailor-made for them, right?

816
00:30:42,920 --> 00:30:45,400
If it's a generic one, how will they

817
00:30:45,400 --> 00:30:47,720
know what is the vendor code business is keeping?

818
00:30:47,720 --> 00:30:48,720
They don't need to know.

819
00:30:48,720 --> 00:30:49,960
That's exactly what I'm saying.

820
00:30:49,960 --> 00:30:52,800
It's associated with the vendor profile.

821
00:30:52,800 --> 00:30:56,240
But vendor profile also, business has to create, right?

822
00:30:56,240 --> 00:30:58,840
So when they onboard the vendors, they'll put a code.

823
00:30:58,920 --> 00:30:59,680
They'll put a code.

824
00:30:59,680 --> 00:31:00,920
With that code, you can look up.

825
00:31:00,920 --> 00:31:01,420
Yeah.

826
00:31:01,420 --> 00:31:01,920
Yeah.

827
00:31:01,920 --> 00:31:04,400
It gives a profile.

828
00:31:04,400 --> 00:31:07,920
We don't have that kind of parts of knowledge.

829
00:31:07,920 --> 00:31:09,640
Then, we are getting into a little bit more.

830
00:31:09,640 --> 00:31:12,640
But can it not be a variable we can reference?

831
00:31:12,640 --> 00:31:16,760
We're trying to move away from all these multiple objects,

832
00:31:16,760 --> 00:31:17,600
no?

833
00:31:17,600 --> 00:31:19,400
How much we can focus on a process?

834
00:31:19,400 --> 00:31:20,800
They also can change it.

835
00:31:20,800 --> 00:31:21,880
But I agree with that.

836
00:31:21,880 --> 00:31:23,760
You give me a value, I change it.

837
00:31:23,760 --> 00:31:24,520
No, no, no.

838
00:31:24,520 --> 00:31:26,120
Why will the vendor change it?

839
00:31:26,120 --> 00:31:28,920
I'll not allow clients to change it, no?

840
00:31:28,920 --> 00:31:31,520
What Bala is doing is, when you create a vendor,

841
00:31:31,520 --> 00:31:33,080
behind the scene, he's putting an ID.

842
00:31:33,080 --> 00:31:33,760
I know.

843
00:31:33,760 --> 00:31:37,680
That means you put a user object that has a lot down there.

844
00:31:37,680 --> 00:31:38,180
ID.

845
00:31:38,180 --> 00:31:41,000
And associate it with that.

846
00:31:41,000 --> 00:31:43,800
When I'm inviting the vendor, I may have a user ID, or.

847
00:31:43,800 --> 00:31:44,300
Yeah.

848
00:31:44,300 --> 00:31:47,320
It gets created in SAP, which I put it here.

849
00:31:47,320 --> 00:31:47,820
Yeah.

850
00:31:47,820 --> 00:31:51,120
That would be pretty helpful, even when I have my virtual board

851
00:31:51,120 --> 00:31:52,400
for anything.

852
00:31:52,400 --> 00:31:57,680
So it means the whole system, not to support in this case.

853
00:31:57,680 --> 00:32:00,920
No, multiple people also, I'll put the same vendor ID.

854
00:32:00,920 --> 00:32:04,240
Vendor ID is a company-level code.

855
00:32:04,240 --> 00:32:06,800
But for the people, what Bala is saying,

856
00:32:06,800 --> 00:32:08,400
if you have 10 people from the vendor,

857
00:32:08,400 --> 00:32:09,320
you have to put the same code.

858
00:32:09,320 --> 00:32:10,040
Yeah, yeah, you have to put the same code.

859
00:32:10,040 --> 00:32:11,800
All that, you have to think of, right?

860
00:32:11,800 --> 00:32:13,480
It's not really easy.

861
00:32:13,480 --> 00:32:18,080
And that means the whole, this new flow of system

862
00:32:18,080 --> 00:32:20,760
was created for this SAP case.

863
00:32:20,800 --> 00:32:22,840
No, no, the other way to look at it is telling.

864
00:32:22,840 --> 00:32:25,240
Change user object.

865
00:32:25,240 --> 00:32:28,520
How about that group, which anyway will be bringing in?

866
00:32:28,520 --> 00:32:32,920
OK, let me go through my demo.

867
00:32:32,920 --> 00:32:34,200
I understand the problems.

868
00:32:34,200 --> 00:32:37,560
But anyway, see, now I've just created this advancement

869
00:32:37,560 --> 00:32:41,160
node between the same vendor communication biker.

870
00:32:41,160 --> 00:32:42,560
So who is seeing this?

871
00:32:42,560 --> 00:32:45,080
The buyer, buyer sees it, actually.

872
00:32:45,080 --> 00:32:46,120
The buyer is?

873
00:32:46,120 --> 00:32:48,360
Buyer meaning the procurement team,

874
00:32:48,360 --> 00:32:52,600
who we created with the.

875
00:32:52,600 --> 00:32:54,360
Does this go to SAP?

876
00:32:54,360 --> 00:32:56,360
No, not at the moment.

877
00:32:56,360 --> 00:32:57,880
It's just a heads up that he's shipping.

878
00:32:57,880 --> 00:32:58,840
Yeah, he's shipping.

879
00:32:58,840 --> 00:33:02,480
But it is more of a nice to have.

880
00:33:02,480 --> 00:33:05,440
People are not really, I mean, we just show it.

881
00:33:05,440 --> 00:33:09,160
What I'm thinking, let's say, it's very good from a process.

882
00:33:09,160 --> 00:33:10,520
It looks very connected.

883
00:33:10,520 --> 00:33:12,680
But the key problem for the business after this

884
00:33:12,680 --> 00:33:14,120
will be to see all this, right?

885
00:33:14,120 --> 00:33:14,720
Yeah, yeah, yeah.

886
00:33:14,720 --> 00:33:16,680
Show me all my vendor advance Japan.

887
00:33:16,680 --> 00:33:18,120
I agree, I agree, yeah, yeah.

888
00:33:18,120 --> 00:33:20,720
Show me all the PRs.

889
00:33:20,720 --> 00:33:23,400
But acknowledgment and ASN, if required,

890
00:33:23,400 --> 00:33:26,560
we can send it as user-defined fields to the SAP.

891
00:33:26,560 --> 00:33:28,640
Because we have the PO number.

892
00:33:28,640 --> 00:33:30,560
We have everything, all the details, right?

893
00:33:30,560 --> 00:33:32,880
Which PO, which PO line item, and all that.

894
00:33:32,880 --> 00:33:34,320
Behind the scenes, we have all that.

895
00:33:34,320 --> 00:33:37,720
Will business look at our system as a process system?

896
00:33:37,720 --> 00:33:38,840
Process, process.

897
00:33:38,840 --> 00:33:41,520
Will also need us to build functional modules?

898
00:33:41,520 --> 00:33:44,360
No, no, they want all this data to go back to SAP.

899
00:33:44,360 --> 00:33:47,360
Then if ASN is not really making it to SAP,

900
00:33:47,400 --> 00:33:50,360
then there's no reason for us also to accept this, right?

901
00:33:50,360 --> 00:33:53,680
And the other way, they'll ask us to list ASNs, right?

902
00:33:53,680 --> 00:33:55,040
True, true.

903
00:33:55,040 --> 00:33:56,480
Whereas they will see it.

904
00:33:56,480 --> 00:33:57,640
That's true, that's true.

905
00:33:57,640 --> 00:33:59,480
Yeah, yeah, yeah.

906
00:33:59,480 --> 00:34:01,640
If you're connecting two systems, perfect, right?

907
00:34:01,640 --> 00:34:01,960
Yeah, yeah, yeah.

908
00:34:01,960 --> 00:34:03,680
If you're owning something, then we

909
00:34:03,680 --> 00:34:06,520
become the source as well, right?

910
00:34:06,520 --> 00:34:08,800
Can make it a little tricky.

911
00:34:08,800 --> 00:34:09,280
OK.

912
00:34:09,280 --> 00:34:10,520
Values I totally get, Bala.

913
00:34:10,520 --> 00:34:12,440
My question is not about evacuation.

914
00:34:12,440 --> 00:34:14,600
Yeah, I looked at it more as a notification.

915
00:34:14,600 --> 00:34:16,920
Yeah, and in your group workspace,

916
00:34:16,960 --> 00:34:18,400
this is a perfect fit setting.

917
00:34:18,400 --> 00:34:21,240
Yeah, yeah, yeah, I agree, yeah.

918
00:34:21,240 --> 00:34:23,880
But I thought the search can be just a workspace search,

919
00:34:23,880 --> 00:34:28,320
saying, you know, if I search for this.

920
00:34:28,320 --> 00:34:29,040
It makes sense.

921
00:34:29,040 --> 00:34:31,320
And any emails also, we can look at it already.

922
00:34:31,320 --> 00:34:31,960
Yeah, yeah.

923
00:34:31,960 --> 00:34:34,880
Anyway, I mean, I'm just showing the entire workflow right now.

924
00:34:34,880 --> 00:34:35,880
Got it.

925
00:34:35,880 --> 00:34:38,800
So now I'm ready to receive the goods in the warehouse.

926
00:34:38,800 --> 00:34:40,680
When they're shipped and the physical goods

927
00:34:40,680 --> 00:34:43,000
have come to the warehouse, OK?

928
00:34:43,000 --> 00:34:45,280
Now I'm starting this process.

929
00:34:45,280 --> 00:34:48,160
The warehouse rep I started.

930
00:34:48,160 --> 00:34:51,200
The warehouse rep sits on the business portal.

931
00:34:51,200 --> 00:34:51,800
Correct.

932
00:34:51,800 --> 00:34:54,200
They should be outside, no, Bala?

933
00:34:54,200 --> 00:34:55,400
Why?

934
00:34:55,400 --> 00:34:59,320
Business and warehouses could be different departments, right?

935
00:34:59,320 --> 00:35:02,520
Yeah, yeah, but they all have access to the one portal.

936
00:35:02,520 --> 00:35:03,320
They're the staff.

937
00:35:03,320 --> 00:35:04,760
They just give us a starting.

938
00:35:04,760 --> 00:35:07,000
And I'm just thinking, in real world,

939
00:35:07,000 --> 00:35:09,240
they may not be a real org member, right?

940
00:35:09,240 --> 00:35:12,160
They will be an assignee, a external party, right?

941
00:35:12,160 --> 00:35:13,000
No, no, no.

942
00:35:13,000 --> 00:35:15,600
Warehouse is an employee of the organization, right?

943
00:35:15,600 --> 00:35:18,280
Employee, but they are not a controller.

944
00:35:18,280 --> 00:35:19,760
Process.

945
00:35:19,760 --> 00:35:22,800
So you can use starting to pick up.

946
00:35:22,800 --> 00:35:23,640
You can do that.

947
00:35:23,640 --> 00:35:24,840
Yeah, yeah, you can do that.

948
00:35:24,840 --> 00:35:25,560
You can do that.

949
00:35:25,560 --> 00:35:27,360
So one start link for warehouse.

950
00:35:27,360 --> 00:35:27,860
Yeah.

951
00:35:36,400 --> 00:35:38,400
So this is done, right?

952
00:35:38,400 --> 00:35:40,080
So now I have to select items.

953
00:35:40,080 --> 00:35:43,400
I do this only because there's no way to select items, right?

954
00:35:43,400 --> 00:35:46,560
One second, Bala, can I go back?

955
00:35:46,560 --> 00:35:48,520
Yeah, I came from here.

956
00:35:48,520 --> 00:35:49,360
Launch my back.

957
00:35:49,360 --> 00:35:51,240
Well, launch my back, OK.

958
00:35:51,240 --> 00:35:53,880
And then the warehouse guy has to first select the PO.

959
00:35:53,880 --> 00:35:56,600
Actually, he has to select the vendor who has sent that,

960
00:35:56,600 --> 00:35:58,200
because he'll know which vendor he has sent.

961
00:35:58,200 --> 00:36:00,000
But if I select the PO, the vendor should be already there.

962
00:36:00,000 --> 00:36:00,960
That's also fine.

963
00:36:00,960 --> 00:36:02,280
But I have done it this way.

964
00:36:02,280 --> 00:36:04,200
Yeah, just checking.

965
00:36:04,200 --> 00:36:08,920
Will they have, will they book one GRN per PO,

966
00:36:08,920 --> 00:36:12,720
or they can put multiple POs in one GRN?

967
00:36:12,720 --> 00:36:14,440
They can do multiple POs in one GRN.

968
00:36:14,440 --> 00:36:17,200
OK, so PO is not the key then.

969
00:36:17,200 --> 00:36:19,200
GRN is the, GRN is the.

970
00:36:19,200 --> 00:36:22,080
We could enforce one GRN for one PO.

971
00:36:22,080 --> 00:36:23,120
One PO?

972
00:36:23,120 --> 00:36:24,320
That is also OK.

973
00:36:24,320 --> 00:36:25,040
It's not a problem.

974
00:36:25,040 --> 00:36:26,720
Then whatever, then when they kickstart,

975
00:36:26,720 --> 00:36:29,160
that PO could be the input for the flow, right?

976
00:36:29,160 --> 00:36:29,680
Correct.

977
00:36:29,680 --> 00:36:31,320
As a variable?

978
00:36:31,320 --> 00:36:32,680
PO, yeah, they can do that.

979
00:36:32,680 --> 00:36:34,320
Then you can avoid this whole thing, right?

980
00:36:34,320 --> 00:36:35,840
We can already look up and come, right?

981
00:36:35,840 --> 00:36:37,160
Yeah, yeah, yeah.

982
00:36:37,200 --> 00:36:41,640
So I can say, create this GRN for these two items, right?

983
00:36:41,640 --> 00:36:44,120
But again, what items is something they have to decide.

984
00:36:44,120 --> 00:36:44,520
Correct.

985
00:36:44,520 --> 00:36:46,920
Because they know, know, they physically receive the items.

986
00:36:46,920 --> 00:36:48,840
No, I'm saying, when they start the flow,

987
00:36:48,840 --> 00:36:50,800
the only thing they are sure of to start the flow

988
00:36:50,800 --> 00:36:51,760
is the PO number, right?

989
00:36:51,760 --> 00:36:52,840
PO number and the item.

990
00:36:52,840 --> 00:36:54,640
That they need another way to enter, right?

991
00:36:54,640 --> 00:36:56,040
Some form or something.

992
00:36:56,040 --> 00:36:57,520
Now you're giving them a quick link,

993
00:36:57,520 --> 00:36:58,560
so it's easy to select.

994
00:36:58,560 --> 00:37:00,080
Correct.

995
00:37:00,080 --> 00:37:02,440
And do they really need to see so much data?

996
00:37:02,440 --> 00:37:03,960
Even today, they will not see, right?

997
00:37:03,960 --> 00:37:05,920
That's very configurable, Manu.

998
00:37:05,960 --> 00:37:08,480
No, but today, we cannot see so much, right?

999
00:37:08,480 --> 00:37:10,080
You don't have SAP access, right?

1000
00:37:10,080 --> 00:37:11,640
Yeah, but he'll need to see.

1001
00:37:11,640 --> 00:37:13,600
He'll only see what is there in that GRN, right?

1002
00:37:13,600 --> 00:37:18,640
No, no, but if you see now, the data that we are bringing,

1003
00:37:18,640 --> 00:37:20,200
let's look at the GRN entry form.

1004
00:37:20,200 --> 00:37:21,960
I'm not showing a whole lot of data.

1005
00:37:21,960 --> 00:37:23,240
Let's look at that.

1006
00:37:23,240 --> 00:37:24,120
No, I am not.

1007
00:37:24,120 --> 00:37:25,720
The vendor is there, right?

1008
00:37:25,720 --> 00:37:26,880
No, no, let's wait.

1009
00:37:26,880 --> 00:37:29,480
See, item code in SAP, item description, price,

1010
00:37:29,480 --> 00:37:31,880
they need currency, order quantity.

1011
00:37:31,880 --> 00:37:33,240
That's all they have.

1012
00:37:33,240 --> 00:37:35,440
No, my question is, currently, when

1013
00:37:35,440 --> 00:37:38,640
they're putting a GRN, who is telling them all these things

1014
00:37:38,640 --> 00:37:40,840
in the GRN notice, right?

1015
00:37:40,840 --> 00:37:43,480
The truck driver will come and give him a PO number.

1016
00:37:43,480 --> 00:37:44,800
PO number and items.

1017
00:37:44,800 --> 00:37:45,320
Items.

1018
00:37:45,320 --> 00:37:46,080
Yeah, yeah, yeah.

1019
00:37:46,080 --> 00:37:47,800
All the other information, even today also,

1020
00:37:47,800 --> 00:37:49,680
you don't know, right?

1021
00:37:49,680 --> 00:37:51,280
His job is just to record it, right?

1022
00:37:51,280 --> 00:37:52,520
We can easily change that.

1023
00:37:52,520 --> 00:37:55,640
Just thinking, what is that he has today?

1024
00:37:55,640 --> 00:37:58,320
So even in the step before, he may not

1025
00:37:58,320 --> 00:38:00,360
need to know the whole details of the PO, right?

1026
00:38:00,360 --> 00:38:00,840
Yeah, yeah, yeah.

1027
00:38:00,840 --> 00:38:02,040
All he knows is the PO number.

1028
00:38:02,040 --> 00:38:03,880
PO number and the item, yeah.

1029
00:38:03,880 --> 00:38:04,560
Items.

1030
00:38:04,560 --> 00:38:06,920
What he really sees in the GRN.

1031
00:38:06,920 --> 00:38:09,080
So we're capturing just the GRN, right?

1032
00:38:09,080 --> 00:38:10,360
That, I think, can go this way.

1033
00:38:10,360 --> 00:38:12,840
No, no, GRN is a document created by the warehouse.

1034
00:38:12,840 --> 00:38:13,480
Understood.

1035
00:38:13,480 --> 00:38:14,600
That is what they put up.

1036
00:38:14,600 --> 00:38:16,720
That will send a shipment notification, no?

1037
00:38:16,720 --> 00:38:19,880
And the shipment, he'll send a hard copy saying,

1038
00:38:19,880 --> 00:38:22,880
against this PO, I have shipped this, this, this item.

1039
00:38:22,880 --> 00:38:24,600
Now, they have to reconcile and.

1040
00:38:24,600 --> 00:38:29,240
So in the same truck, you can get multiple POs delivered also.

1041
00:38:29,240 --> 00:38:32,320
So technically reconciling and just booking it.

1042
00:38:32,320 --> 00:38:32,800
Booking it, yeah.

1043
00:38:32,840 --> 00:38:35,240
Which is something like that style which Stanley showed.

1044
00:38:35,240 --> 00:38:37,960
PO number, items, and quantity.

1045
00:38:37,960 --> 00:38:39,760
Here, we need to have a check saying

1046
00:38:39,760 --> 00:38:43,480
this should not be more than the pending quantity.

1047
00:38:43,480 --> 00:38:44,560
Receive quantity.

1048
00:38:44,560 --> 00:38:46,080
This is a runtime validation.

1049
00:38:46,080 --> 00:38:47,960
Validation we have to do.

1050
00:38:47,960 --> 00:38:49,240
Right now, I'm not done.

1051
00:38:49,240 --> 00:38:51,440
I mean, there's no way to do it, yeah.

1052
00:38:51,440 --> 00:38:52,800
So I say QC required.

1053
00:38:52,800 --> 00:38:53,280
That's it.

1054
00:38:53,280 --> 00:38:54,360
That's all I can do.

1055
00:38:54,360 --> 00:38:55,880
Let's say we send the wrong quantity.

1056
00:38:55,880 --> 00:38:57,480
SAP will reject it?

1057
00:38:57,480 --> 00:39:00,160
No, no, they accept whatever.

1058
00:39:00,160 --> 00:39:01,800
You're saying then what is our value?

1059
00:39:01,800 --> 00:39:05,760
No, no, SAP also behaves as if it doesn't know anything.

1060
00:39:05,760 --> 00:39:07,840
If you don't validate, we are not adding value.

1061
00:39:07,840 --> 00:39:09,560
It becomes pure data entry.

1062
00:39:09,560 --> 00:39:11,760
No, but see, look at it this way, right?

1063
00:39:11,760 --> 00:39:13,720
You could, the vendor might actually

1064
00:39:13,720 --> 00:39:16,360
ship more than what you have ordered.

1065
00:39:16,360 --> 00:39:17,440
That's possible.

1066
00:39:17,440 --> 00:39:19,360
So we have to look at the risk-based use case.

1067
00:39:19,360 --> 00:39:24,000
So along with that item list, if I also get to take a table,

1068
00:39:24,000 --> 00:39:26,200
behind the scene, not the UI table, I'm saying.

1069
00:39:26,200 --> 00:39:30,280
For each item, I know what is received, what is pending,

1070
00:39:30,360 --> 00:39:32,800
what is the description, whatever I need,

1071
00:39:32,800 --> 00:39:35,560
then I can use that even for validation.

1072
00:39:35,560 --> 00:39:39,640
If I enter, say, 100, then only 20 is pending to receive.

1073
00:39:39,640 --> 00:39:40,560
See if something is wrong.

1074
00:39:43,360 --> 00:39:46,760
So let's say I'm receiving 20 out of this.

1075
00:39:46,760 --> 00:39:48,920
And for this, I am making it no.

1076
00:39:48,920 --> 00:39:54,840
So if any of the QA items are this one, marked as yes,

1077
00:39:54,840 --> 00:39:56,760
then the QC process starts.

1078
00:39:56,760 --> 00:39:58,520
So far, so good, Stanley?

1079
00:39:58,520 --> 00:39:59,520
Yeah.

1080
00:39:59,520 --> 00:40:03,360
So as a QA guy, I have to just review this and say,

1081
00:40:03,360 --> 00:40:05,440
is this QC passed?

1082
00:40:05,440 --> 00:40:07,680
Actually, here, technically, what could happen

1083
00:40:07,680 --> 00:40:09,880
is they could reject part of the quantity also,

1084
00:40:09,880 --> 00:40:11,040
but we are not handling that.

1085
00:40:11,040 --> 00:40:13,600
We don't need to add that right now.

1086
00:40:13,600 --> 00:40:14,760
Got it.

1087
00:40:14,760 --> 00:40:17,880
So I have gone in here, right?

1088
00:40:17,880 --> 00:40:21,200
And then the SAP should have that info, right?

1089
00:40:21,200 --> 00:40:22,480
Yeah, I have not modeled that.

1090
00:40:22,480 --> 00:40:23,480
But it is possible, right?

1091
00:40:23,480 --> 00:40:25,160
Yeah, yeah, yeah, it's possible, yeah.

1092
00:40:25,160 --> 00:40:26,640
That's all like building on top of it.

1093
00:40:26,640 --> 00:40:29,160
Yeah, I have to do a workshop to understand

1094
00:40:30,000 --> 00:40:32,000
how they want it and all that.

1095
00:40:32,000 --> 00:40:33,000
Anyway, this is done.

1096
00:40:33,000 --> 00:40:34,200
And now I do it.

1097
00:40:34,200 --> 00:40:37,000
It will create the JRN-PO and SAP.

1098
00:40:37,000 --> 00:40:42,160
That completes the whole loop from request to purchase orders

1099
00:40:42,160 --> 00:40:44,080
to JRNs.

1100
00:40:44,080 --> 00:40:46,840
And this is one major pain point for them today, right?

1101
00:40:46,840 --> 00:40:47,600
Correct, correct.

1102
00:40:47,600 --> 00:40:48,760
Yeah, yeah, yeah.

1103
00:40:48,760 --> 00:40:56,440
Now, if I go here, I can see there is a new reading about.

1104
00:40:56,480 --> 00:40:58,680
I get so confused with these guys.

1105
00:41:01,280 --> 00:41:02,280
Now, let's see.

1106
00:41:07,920 --> 00:41:09,200
Let's close this.

1107
00:41:09,200 --> 00:41:11,680
Now I go to Goods Receipt PO.

1108
00:41:11,680 --> 00:41:13,040
I go to the last one.

1109
00:41:13,040 --> 00:41:16,400
So you'll see 30 and 20, which we received.

1110
00:41:16,400 --> 00:41:18,040
There's one that got just now created.

1111
00:41:18,040 --> 00:41:19,240
Yeah, yeah, yeah.

1112
00:41:19,240 --> 00:41:21,400
And you can see the purchase order reference here.

1113
00:41:21,400 --> 00:41:22,360
How did you call SAP Bala?

1114
00:41:22,360 --> 00:41:24,440
From Mozilla SAP, what is that story?

1115
00:41:24,440 --> 00:41:25,520
API, yeah?

1116
00:41:25,520 --> 00:41:28,520
There are two calls you have to make for business one.

1117
00:41:28,520 --> 00:41:31,960
One to get the session ID, which is like our access token.

1118
00:41:31,960 --> 00:41:33,880
And one is the payload.

1119
00:41:33,880 --> 00:41:34,440
That's a post.

1120
00:41:34,440 --> 00:41:37,680
And also, he exposed our MaxPost ID.

1121
00:41:37,680 --> 00:41:40,400
Do the same DI layer.

1122
00:41:40,400 --> 00:41:41,160
It's fairly easy.

1123
00:41:41,160 --> 00:41:44,080
He completes it in, if I tell him some change,

1124
00:41:44,080 --> 00:41:45,600
he'll do it in 10 minutes.

1125
00:41:45,600 --> 00:41:47,360
So APIs and all, he can expose.

1126
00:41:47,360 --> 00:41:48,120
Yeah, yeah, yeah.

1127
00:41:48,120 --> 00:41:49,240
API is very easy.

1128
00:41:49,240 --> 00:41:50,440
Any object, he can expose.

1129
00:41:50,440 --> 00:41:53,600
Yeah, yeah, any object.

1130
00:41:53,600 --> 00:41:55,800
Now you can see the underlying document also.

1131
00:41:55,800 --> 00:41:56,680
Somewhere it is there.

1132
00:41:56,680 --> 00:41:58,480
See, related document, base document,

1133
00:41:58,480 --> 00:42:00,920
you can go to the purchase order here.

1134
00:42:00,920 --> 00:42:03,760
So it's all linked, Stanley.

1135
00:42:03,760 --> 00:42:06,000
So they can, it's all done properly

1136
00:42:06,000 --> 00:42:08,920
so that you get the base document.

1137
00:42:08,920 --> 00:42:11,440
And you can see the comments here also based on purchase

1138
00:42:11,440 --> 00:42:12,960
because this we didn't enter.

1139
00:42:12,960 --> 00:42:16,200
Once you send the information correctly,

1140
00:42:16,200 --> 00:42:19,680
SAP will do all the remarks and all that.

1141
00:42:19,680 --> 00:42:22,360
Now it's all about abstracting the interaction

1142
00:42:22,400 --> 00:42:24,000
apart from the mock-up, right?

1143
00:42:24,000 --> 00:42:24,840
Yeah.

1144
00:42:24,840 --> 00:42:26,440
Where to start, what to enter, what to,

1145
00:42:26,440 --> 00:42:29,080
sending and getting everything connected will be there.

1146
00:42:30,200 --> 00:42:31,040
Most likely,

1147
00:42:32,560 --> 00:42:35,640
it is the professional service

1148
00:42:35,640 --> 00:42:37,680
that we've been using for a long time.

1149
00:42:37,680 --> 00:42:42,400
So this is more about mock-up framework,

1150
00:42:42,400 --> 00:42:46,600
how to make our own professional service team

1151
00:42:46,600 --> 00:42:49,160
easier to be customized.

1152
00:42:49,160 --> 00:42:50,000
Yeah.

1153
00:42:50,960 --> 00:42:55,200
Yeah, they should be able to just configure the fields

1154
00:42:55,200 --> 00:42:56,760
and it should work.

1155
00:42:56,760 --> 00:42:58,080
And we can do that.

1156
00:42:58,080 --> 00:42:58,920
Yeah.

1157
00:42:58,920 --> 00:43:00,240
So at the end, you have a body of process.

1158
00:43:00,240 --> 00:43:01,080
Yeah, yeah.

1159
00:43:02,240 --> 00:43:03,080
Yeah, yeah.

1160
00:43:07,880 --> 00:43:10,440
Or the partner, partner can configure, Stanley.

1161
00:43:10,440 --> 00:43:12,280
If you make it, yeah, yeah.

1162
00:43:12,280 --> 00:43:14,680
If you make it easy, right?

1163
00:43:14,680 --> 00:43:16,920
Then we train the partner,

1164
00:43:16,920 --> 00:43:19,080
they themselves can implement Boxo.

1165
00:43:20,000 --> 00:43:24,080
So there's no self-service for this kind of work.

1166
00:43:24,080 --> 00:43:25,040
It's tough, Stanley,

1167
00:43:25,040 --> 00:43:27,440
because businesses are very different

1168
00:43:27,440 --> 00:43:28,920
in how they use the system.

1169
00:43:28,920 --> 00:43:30,000
It's not our fault.

1170
00:43:30,000 --> 00:43:34,520
We are building it to work for our professional service

1171
00:43:34,520 --> 00:43:35,360
and the partner.

1172
00:43:36,440 --> 00:43:39,880
What is the extent we will go to make it,

1173
00:43:41,040 --> 00:43:43,760
like they will understand only certain language, right?

1174
00:43:43,760 --> 00:43:46,480
We use, so, for close it's clear.

1175
00:43:46,480 --> 00:43:49,160
But how about those objects like vendors,

1176
00:43:49,160 --> 00:43:50,000
like...

1177
00:43:51,560 --> 00:43:54,040
We do not have that in the form.

1178
00:43:54,040 --> 00:43:58,240
We just will be related from the SAP and in the form.

1179
00:43:58,240 --> 00:44:00,240
So that's what I was asking.

1180
00:44:00,240 --> 00:44:02,600
We can have vendor onboarding and create vendor.

1181
00:44:02,600 --> 00:44:05,200
We can come up with a set of use cases if you want.

1182
00:44:05,200 --> 00:44:06,040
Well, like for example, Stanley.

1183
00:44:06,040 --> 00:44:07,400
Set of workflows, right?

1184
00:44:07,400 --> 00:44:08,240
Yes.

1185
00:44:08,240 --> 00:44:10,760
For vendor onboarding, we can create a workflow.

1186
00:44:10,760 --> 00:44:11,840
Also it'll be a flow.

1187
00:44:11,840 --> 00:44:13,360
Yeah, it'll be a flow.

1188
00:44:13,360 --> 00:44:14,640
And then at the end of it,

1189
00:44:14,640 --> 00:44:16,800
we'll send SAP to create a vendor.

1190
00:44:16,800 --> 00:44:17,640
Yes.

1191
00:44:17,640 --> 00:44:18,480
Yeah.

1192
00:44:18,640 --> 00:44:20,160
How do we work with these?

1193
00:44:20,160 --> 00:44:22,560
There's no any such...

1194
00:44:22,560 --> 00:44:25,200
We know it by the way how we work.

1195
00:44:25,200 --> 00:44:29,800
Our system should be to connect between these two points,

1196
00:44:29,800 --> 00:44:34,800
business and the field or the gap between the SAP system

1197
00:44:36,480 --> 00:44:38,360
and the rest of the current process.

1198
00:44:38,360 --> 00:44:40,760
Like we ought to be digitizing all those emails.

1199
00:44:40,760 --> 00:44:41,600
Yeah.

1200
00:44:41,600 --> 00:44:42,440
All right.

1201
00:44:42,440 --> 00:44:45,080
We should not never become a source system to track data.

1202
00:44:45,080 --> 00:44:45,920
Yeah, yeah.

1203
00:44:45,920 --> 00:44:47,560
That is one thing we should be very careful of.

1204
00:44:47,560 --> 00:44:48,400
Correct, correct.

1205
00:44:48,400 --> 00:44:49,240
Yeah, yeah.

1206
00:44:49,240 --> 00:44:50,060
Because if they say,

1207
00:44:50,060 --> 00:44:51,280
I want to track the ARNs in your system,

1208
00:44:51,280 --> 00:44:52,560
that is a very different schedule.

1209
00:44:52,560 --> 00:44:53,400
Correct, correct.

1210
00:44:53,400 --> 00:44:54,240
Yeah, yeah, yeah.

1211
00:44:54,240 --> 00:44:55,080
We should not.

1212
00:44:55,080 --> 00:44:55,920
We should not.

1213
00:44:55,920 --> 00:45:00,160
Even about process, we do not customize.

1214
00:45:01,360 --> 00:45:06,120
We do not customize for one specific system

1215
00:45:06,120 --> 00:45:11,120
but provide a general capability for any similar system.

1216
00:45:12,120 --> 00:45:15,160
We can issue a query.

1217
00:45:16,320 --> 00:45:19,160
We can issue a query to write, to feed.

1218
00:45:19,160 --> 00:45:21,400
When we read, we're supporting the reader.

1219
00:45:21,400 --> 00:45:24,360
Table is the way to support the reader.

1220
00:45:24,360 --> 00:45:28,080
But one is the view and we can display a table.

1221
00:45:28,080 --> 00:45:30,400
We can put it into the countdown.

1222
00:45:30,400 --> 00:45:31,240
Yeah.

1223
00:45:32,520 --> 00:45:35,160
So to us, everything is...

1224
00:45:35,160 --> 00:45:36,280
Input and output.

1225
00:45:36,280 --> 00:45:37,240
Yeah, input and output.

1226
00:45:37,240 --> 00:45:40,760
But even type of input for the dependency

1227
00:45:41,080 --> 00:45:43,560
of first input to the second input.

1228
00:45:43,560 --> 00:45:44,400
Yeah.

1229
00:45:44,400 --> 00:45:49,400
So it doesn't depend on the input output types.

1230
00:45:49,840 --> 00:45:52,760
And we have connector types.

1231
00:45:53,880 --> 00:45:55,960
If we have something that we may not expect,

1232
00:45:55,960 --> 00:46:00,000
we may not use it inside the computer.

1233
00:46:00,000 --> 00:46:01,280
Connector.

1234
00:46:01,280 --> 00:46:04,920
When I say connector, it means the automation connector.

1235
00:46:04,920 --> 00:46:06,160
App logic.

1236
00:46:06,160 --> 00:46:07,000
That.

1237
00:46:07,000 --> 00:46:09,080
Means those in the collision logic.

1238
00:46:09,120 --> 00:46:11,360
Get PO, update PO.

1239
00:46:11,360 --> 00:46:12,920
Innovation apps.

1240
00:46:12,920 --> 00:46:15,040
Automation apps, trigger apps.

1241
00:46:15,040 --> 00:46:16,520
Logic.

1242
00:46:16,520 --> 00:46:18,160
Yeah, that's true.

1243
00:46:18,160 --> 00:46:19,760
The only they can build it anyway.

1244
00:46:19,760 --> 00:46:23,880
And then our part of the service that...

1245
00:46:23,880 --> 00:46:25,560
Configure it for them.

1246
00:46:25,560 --> 00:46:28,720
To configure it for the...

1247
00:46:28,720 --> 00:46:30,480
So we show a standard demo.

1248
00:46:30,480 --> 00:46:34,280
Then we get into a gap analysis workshop

1249
00:46:34,280 --> 00:46:35,640
and configure their flow.

1250
00:46:35,640 --> 00:46:36,480
Yeah.

1251
00:46:40,080 --> 00:46:40,920
So this...

1252
00:46:44,800 --> 00:46:45,640
Yeah.

1253
00:46:46,520 --> 00:46:47,760
No, if you give it to the partner,

1254
00:46:47,760 --> 00:46:49,720
they'll do it day in and day out.

1255
00:46:49,720 --> 00:46:51,840
So if you make it partner-ready, no?

1256
00:46:51,840 --> 00:46:53,360
That's all.

1257
00:46:53,360 --> 00:46:57,080
They will do, happily do services and get money.

1258
00:46:57,080 --> 00:46:58,360
That flow is already asking me,

1259
00:46:58,360 --> 00:46:59,920
how much revenue will you share?

1260
00:47:00,920 --> 00:47:02,200
The Spain.

1261
00:47:02,200 --> 00:47:03,040
The Spain.

1262
00:47:03,040 --> 00:47:03,880
Spain, yeah.

1263
00:47:03,880 --> 00:47:06,640
It was like, I said 20% on license.

1264
00:47:06,640 --> 00:47:08,240
I said, yeah, yeah, okay.

1265
00:47:08,280 --> 00:47:11,920
What if, you know, we refer and you do the work?

1266
00:47:11,920 --> 00:47:13,320
I said, okay, 10%.

1267
00:47:15,320 --> 00:47:16,840
One thing where I feel...

1268
00:47:19,160 --> 00:47:20,920
Referral agreement and reseller.

1269
00:47:20,920 --> 00:47:23,160
I mean, we have a reseller agreement.

1270
00:47:23,160 --> 00:47:26,360
They are asking how much revenue share we do.

1271
00:47:26,360 --> 00:47:27,200
They do it.

1272
00:47:27,200 --> 00:47:28,600
We give that 10%.

1273
00:47:28,600 --> 00:47:29,440
Yeah, yeah.

1274
00:47:30,320 --> 00:47:31,640
No, no.

1275
00:47:31,640 --> 00:47:32,800
They refer to us.

1276
00:47:32,800 --> 00:47:33,760
They refer to us.

1277
00:47:33,760 --> 00:47:36,520
We give 10% of what we make.

1278
00:47:36,520 --> 00:47:37,360
Yeah, yeah.

1279
00:47:38,560 --> 00:47:39,640
If they do it.

1280
00:47:40,720 --> 00:47:43,880
If they sell the whole thing and do it,

1281
00:47:43,880 --> 00:47:45,440
then we give them 20%.

1282
00:47:46,760 --> 00:47:48,760
It depends on how we charge.

1283
00:47:48,760 --> 00:47:49,800
Yeah.

1284
00:47:49,800 --> 00:47:51,360
We can give them a price list

1285
00:47:51,360 --> 00:47:53,200
and say, this is what you need to say.

1286
00:47:53,200 --> 00:47:55,080
But our charge will be same as our package, right?

1287
00:47:55,080 --> 00:47:55,920
Yeah, yeah, yeah.

1288
00:47:55,920 --> 00:47:58,960
How about if they found out if they have...

1289
00:47:58,960 --> 00:48:02,200
That's what we need to discuss, Stanley.

1290
00:48:02,200 --> 00:48:03,520
They're ready to do that, right?

1291
00:48:03,520 --> 00:48:04,360
Yeah.

1292
00:48:04,360 --> 00:48:05,600
Because that will be the big sale rate.

1293
00:48:05,600 --> 00:48:06,440
Yeah, yeah, yeah.

1294
00:48:06,440 --> 00:48:07,600
It will be.

1295
00:48:07,600 --> 00:48:09,000
But it's a service perpetual rate.

1296
00:48:09,000 --> 00:48:10,560
When they make the sale,

1297
00:48:10,560 --> 00:48:12,840
how much revenue do you share back to us?

1298
00:48:12,840 --> 00:48:14,080
No, we are actually interested

1299
00:48:14,080 --> 00:48:16,880
in their existing customer base, Stanley.

1300
00:48:16,880 --> 00:48:18,280
They have 5,000...

1301
00:48:18,280 --> 00:48:19,720
They have upsell.

1302
00:48:19,720 --> 00:48:21,040
Yeah, upsell they have to do.

1303
00:48:21,040 --> 00:48:21,880
Yeah, upsell.

1304
00:48:21,880 --> 00:48:23,120
They'll probably sell it as an add-on.

1305
00:48:23,120 --> 00:48:23,960
Add-on, yeah.

1306
00:48:23,960 --> 00:48:25,440
Yeah, they're paying you to this sale

1307
00:48:25,440 --> 00:48:27,040
that you were sharing with us.

1308
00:48:27,040 --> 00:48:28,280
Like, let's say...

1309
00:48:28,280 --> 00:48:29,320
That is how it will be.

1310
00:48:29,320 --> 00:48:30,160
They will only sell.

1311
00:48:30,160 --> 00:48:31,360
They'll sell it, sir.

1312
00:48:31,360 --> 00:48:32,200
Partner sells.

1313
00:48:32,200 --> 00:48:33,040
Yeah, partner sells.

1314
00:48:33,040 --> 00:48:33,880
No, no.

1315
00:48:33,880 --> 00:48:34,720
They'll usually, we'll...

1316
00:48:34,720 --> 00:48:35,560
That's what.

1317
00:48:35,560 --> 00:48:37,760
They'll sell them like 20%.

1318
00:48:37,760 --> 00:48:40,120
Maybe 30% if they are really good

1319
00:48:40,120 --> 00:48:41,400
and they hit some numbers.

1320
00:48:41,400 --> 00:48:42,760
The way they do, Stanley,

1321
00:48:42,760 --> 00:48:44,640
like any reseller, no?

1322
00:48:46,040 --> 00:48:47,800
If they're just referring,

1323
00:48:47,800 --> 00:48:49,640
they'll give 10%.

1324
00:48:49,640 --> 00:48:51,040
Palmstack gives us 10%.

1325
00:48:51,040 --> 00:48:53,280
We referred some deals to them.

1326
00:48:53,280 --> 00:48:55,920
We got 10% out of that.

1327
00:48:55,920 --> 00:48:58,040
Then you go a little higher.

1328
00:48:58,040 --> 00:49:00,200
Like, they do the complete selling.

1329
00:49:01,560 --> 00:49:03,880
Then we get them 20%

1330
00:49:03,880 --> 00:49:06,240
because they're taking the risk and all.

1331
00:49:06,240 --> 00:49:08,880
So, how long?

1332
00:49:08,880 --> 00:49:10,680
Yeah, whatever annual...

1333
00:49:10,680 --> 00:49:13,120
We can hold some terms.

1334
00:49:13,120 --> 00:49:13,960
Yeah, yeah.

1335
00:49:13,960 --> 00:49:15,520
Because that's how they're incentivized

1336
00:49:15,520 --> 00:49:18,160
to keep the customer also.

1337
00:49:18,160 --> 00:49:19,160
I mean, they'll service the customer also.

1338
00:49:19,160 --> 00:49:20,520
That is easy for Palmstack in a way

1339
00:49:20,520 --> 00:49:22,200
because they are also recurring.

1340
00:49:22,200 --> 00:49:23,040
Yeah, yeah, yeah.

1341
00:49:23,040 --> 00:49:24,760
SAP model may be slightly different, no?

1342
00:49:24,760 --> 00:49:25,600
No, no.

1343
00:49:25,600 --> 00:49:26,720
We are charging referring, no?

1344
00:49:26,720 --> 00:49:27,560
Yeah.

1345
00:49:27,560 --> 00:49:29,640
But these guys will be selling one time per perjury.

1346
00:49:29,640 --> 00:49:30,480
No.

1347
00:49:30,480 --> 00:49:31,320
With AMC.

1348
00:49:31,320 --> 00:49:32,200
That they will,

1349
00:49:32,200 --> 00:49:34,400
but we will charge only recurring, no?

1350
00:49:34,400 --> 00:49:36,520
Anyway, our cost will be much lower than them.

1351
00:49:36,520 --> 00:49:37,360
Correct.

1352
00:49:37,360 --> 00:49:40,080
SAP one will be like going in millions.

1353
00:49:40,080 --> 00:49:40,920
No, no.

1354
00:49:40,920 --> 00:49:42,560
Business one is not that expensive,

1355
00:49:42,560 --> 00:49:43,400
but then...

1356
00:49:43,400 --> 00:49:44,960
Should be million number, huh?

1357
00:49:44,960 --> 00:49:45,800
Should be in million, right?

1358
00:49:45,800 --> 00:49:46,640
No, no, no.

1359
00:49:46,640 --> 00:49:47,480
No?

1360
00:49:47,480 --> 00:49:49,280
It was more expensive than...

1361
00:49:49,280 --> 00:49:51,960
Business one is only some 50,000.

1362
00:49:51,960 --> 00:49:53,520
Really?

1363
00:49:53,520 --> 00:49:54,360
Perpetualizing,

1364
00:49:54,360 --> 00:49:56,560
but see, the problem with business one is

1365
00:49:56,560 --> 00:49:57,440
they can't do anything.

1366
00:49:57,440 --> 00:50:01,480
See, there's just one table and no process at all.

1367
00:50:01,520 --> 00:50:02,360
There's no process.

1368
00:50:02,360 --> 00:50:03,320
You see, no?

1369
00:50:03,320 --> 00:50:04,480
There's no process at all.

1370
00:50:04,480 --> 00:50:07,720
It's just some table after table after table.

1371
00:50:07,720 --> 00:50:08,560
That's all.

1372
00:50:08,560 --> 00:50:11,040
So this is a good one to start, isn't it?

1373
00:50:11,040 --> 00:50:11,880
Yeah.

1374
00:50:12,720 --> 00:50:13,920
And same thing you can run with others.

1375
00:50:13,920 --> 00:50:15,680
Okay, let me put it this way.

1376
00:50:15,680 --> 00:50:17,440
Whatever we have quoted, no?

1377
00:50:17,440 --> 00:50:19,080
$28,000 and all,

1378
00:50:19,080 --> 00:50:21,720
people are ready to accept.

1379
00:50:21,720 --> 00:50:23,240
So, per year, right?

1380
00:50:24,960 --> 00:50:28,240
So I would say 20,000 to 30,000

1381
00:50:28,240 --> 00:50:29,960
for a small, mid-sized,

1382
00:50:30,000 --> 00:50:31,840
I mean, like a medium-sized.

1383
00:50:31,840 --> 00:50:33,800
If you go mid-market, you will get more.

1384
00:50:35,520 --> 00:50:36,800
I'm talking about ARR, actually.

1385
00:50:36,800 --> 00:50:39,640
Whatever you showed is the fundamental process, right?

1386
00:50:39,640 --> 00:50:41,240
So without that, they cannot live.

1387
00:50:41,240 --> 00:50:42,080
So the more you do...

1388
00:50:42,080 --> 00:50:43,520
What are they doing in Excel?

1389
00:50:44,640 --> 00:50:46,000
Life-changing.

1390
00:50:46,000 --> 00:50:48,880
But again, one thing I'm still not convinced, Bala,

1391
00:50:48,880 --> 00:50:50,240
after all this,

1392
00:50:50,240 --> 00:50:52,480
they should have a very good system to track, right?

1393
00:50:52,480 --> 00:50:54,880
The SAP becomes that system now, looks like.

1394
00:50:54,880 --> 00:50:57,880
Yeah, because see, the data is there in SAP.

1395
00:50:57,880 --> 00:51:00,560
It's only how it comes to...

1396
00:51:00,560 --> 00:51:01,720
The process.

1397
00:51:01,720 --> 00:51:04,240
But if SAP don't have a certain thing,

1398
00:51:04,240 --> 00:51:05,680
then we also don't have that, right?

1399
00:51:05,680 --> 00:51:06,520
That is how it will become.

1400
00:51:06,520 --> 00:51:08,080
Correct, correct, yeah, yeah.

1401
00:51:08,080 --> 00:51:09,400
Like the ARN, for example.

1402
00:51:09,400 --> 00:51:12,120
If SAP cannot track, we cannot run the process too, right?

1403
00:51:12,120 --> 00:51:12,960
Correct, correct.

1404
00:51:12,960 --> 00:51:15,480
But if you have the cross-reference to SAP,

1405
00:51:15,480 --> 00:51:16,440
like those numbers,

1406
00:51:16,440 --> 00:51:19,120
now we've stored it in the workspace stacks,

1407
00:51:19,120 --> 00:51:19,960
then they can say,

1408
00:51:19,960 --> 00:51:22,840
I received this GRN on this date, right?

1409
00:51:22,840 --> 00:51:24,160
Let me search for it.

1410
00:51:24,160 --> 00:51:26,480
Like, for example, the QA data, right?

1411
00:51:26,480 --> 00:51:30,720
There is no place to store it in SAP, beyond.

1412
00:51:30,720 --> 00:51:31,560
It's only there,

1413
00:51:31,560 --> 00:51:34,040
quality management module is there only in...

1414
00:51:34,040 --> 00:51:37,760
They can use our system for that, in a generic way.

1415
00:51:37,760 --> 00:51:40,120
We will not have any custom listing, like a PO list or...

1416
00:51:40,120 --> 00:51:40,960
No, no, no.

1417
00:51:40,960 --> 00:51:44,280
That's what they'll search for the GRN,

1418
00:51:44,280 --> 00:51:46,480
and then they can see the details within the workspace,

1419
00:51:46,480 --> 00:51:49,680
saying, this is what happened, these are the quality.

1420
00:51:49,680 --> 00:51:53,480
So sadly that, in our premium,

1421
00:51:53,480 --> 00:51:55,320
I don't know how you are thinking on this,

1422
00:51:56,120 --> 00:51:58,240
let's say a lot of flows gets executed,

1423
00:51:58,240 --> 00:52:02,920
a lot of data is getting created, forms, e-science, PDF.

1424
00:52:02,920 --> 00:52:04,680
We should have some way to...

1425
00:52:04,680 --> 00:52:06,640
That is our content list.

1426
00:52:06,640 --> 00:52:08,560
Of course, in our own generic way,

1427
00:52:08,560 --> 00:52:10,840
not anything functional.

1428
00:52:10,840 --> 00:52:13,680
So list of forms, list of documents,

1429
00:52:13,680 --> 00:52:16,080
should be available even in our platform, right?

1430
00:52:17,280 --> 00:52:18,680
I think that's the net.

1431
00:52:18,680 --> 00:52:19,520
Yeah.

1432
00:52:19,520 --> 00:52:20,960
Of course, it's future.

1433
00:52:21,000 --> 00:52:25,000
Because we have so many processes by itself,

1434
00:52:25,000 --> 00:52:26,600
we have to come back on it.

1435
00:52:26,600 --> 00:52:27,440
Yeah, yeah.

1436
00:52:27,440 --> 00:52:29,720
I agree, but not everything is making...

1437
00:52:29,720 --> 00:52:31,720
All those forms, all those happiness,

1438
00:52:31,720 --> 00:52:36,240
the data, should go back to their original system.

1439
00:52:36,240 --> 00:52:37,560
That is the ideal situation.

1440
00:52:37,560 --> 00:52:39,920
But to really SMBs and all, they may...

1441
00:52:39,920 --> 00:52:44,600
We're here, because we have to have your process back.

1442
00:52:44,600 --> 00:52:47,440
If there's any people that want to get to operate...

1443
00:52:47,440 --> 00:52:49,560
No, I'm just saying...

1444
00:52:49,560 --> 00:52:51,400
No matter what we do,

1445
00:52:51,400 --> 00:52:53,200
it can only mean that there's a requirement.

1446
00:52:53,200 --> 00:52:54,640
I mean, they would have not had it.

1447
00:52:54,640 --> 00:52:56,840
No, I totally agree.

1448
00:52:56,840 --> 00:52:58,880
If there is a source or a target system,

1449
00:52:58,880 --> 00:53:00,400
definitely it should go.

1450
00:53:00,400 --> 00:53:02,680
Things like vendor onboarding, right?

1451
00:53:02,680 --> 00:53:05,200
So they must have built some forms.

1452
00:53:05,200 --> 00:53:07,120
If they don't have a CRM,

1453
00:53:07,120 --> 00:53:10,840
they're using Moxor to send out vendor onboarding process.

1454
00:53:10,840 --> 00:53:12,640
So wouldn't they need a place to go back

1455
00:53:12,640 --> 00:53:14,720
and search that, what he built?

1456
00:53:14,720 --> 00:53:18,200
They should have automation to put in the book or sheet.

1457
00:53:18,240 --> 00:53:20,840
Yeah, so that is the decision we are making then.

1458
00:53:20,840 --> 00:53:21,920
We'll never...

1459
00:53:21,920 --> 00:53:23,160
But that's the data we have.

1460
00:53:23,160 --> 00:53:25,040
We know what it is.

1461
00:53:25,040 --> 00:53:27,680
We have a way to not want to provide it as value.

1462
00:53:27,680 --> 00:53:30,720
We want to provide more process value.

1463
00:53:30,720 --> 00:53:32,640
All the things about the project,

1464
00:53:32,640 --> 00:53:34,920
not beyond the project.

1465
00:53:34,920 --> 00:53:37,600
So content search would want to use that.

1466
00:53:37,600 --> 00:53:38,800
The complex.

1467
00:53:38,800 --> 00:53:39,640
And the domain.

1468
00:53:39,640 --> 00:53:43,320
Even the community, even the best job world,

1469
00:53:43,320 --> 00:53:45,880
when they're managing them.

1470
00:53:45,880 --> 00:53:47,520
I'm not talking vendor management

1471
00:53:47,520 --> 00:53:49,040
as a functionality.

1472
00:53:49,040 --> 00:53:50,760
List of forms.

1473
00:53:50,760 --> 00:53:51,760
List of documents.

1474
00:53:52,680 --> 00:53:53,520
List of design.

1475
00:53:53,520 --> 00:53:54,640
We can discuss,

1476
00:53:54,640 --> 00:53:56,560
even from a utility perspective.

1477
00:53:57,960 --> 00:53:59,120
When you're in the front of your utility,

1478
00:53:59,120 --> 00:54:03,720
there's not, say, some application that go back.

1479
00:54:03,720 --> 00:54:04,960
Because these lists,

1480
00:54:04,960 --> 00:54:06,480
there's no way you can manage it.

1481
00:54:06,480 --> 00:54:10,080
There's no way you can get the utility from that.

1482
00:54:10,080 --> 00:54:11,200
That is just, you know,

1483
00:54:11,200 --> 00:54:12,320
if you have a salary,

1484
00:54:12,320 --> 00:54:16,000
there's no way for you to say you get something.

1485
00:54:16,000 --> 00:54:18,680
Why does the frequency of search

1486
00:54:18,680 --> 00:54:21,440
manage it in this flow chart,

1487
00:54:21,440 --> 00:54:23,080
chart management,

1488
00:54:23,080 --> 00:54:25,080
and you're going to perform data.

1489
00:54:25,080 --> 00:54:26,840
You're going to be out.

1490
00:54:26,840 --> 00:54:27,680
But why not?

1491
00:54:27,680 --> 00:54:28,520
If you don't put it in the form,

1492
00:54:28,520 --> 00:54:29,720
you're going to be out.

1493
00:54:29,720 --> 00:54:30,560
You can take it out.

1494
00:54:30,560 --> 00:54:31,840
Yeah, you should take it out.

1495
00:54:31,840 --> 00:54:34,280
I think all mid-size will be perfectly fine,

1496
00:54:34,280 --> 00:54:35,600
what we discussed.

1497
00:54:35,600 --> 00:54:38,120
It's only if a person who do not have such things,

1498
00:54:38,120 --> 00:54:39,280
and data is with us,

1499
00:54:39,280 --> 00:54:40,120
is there anything you can do?

1500
00:54:40,120 --> 00:54:43,600
We want to scale on the moral process used here.

1501
00:54:43,600 --> 00:54:48,600
The whole process related stuff is going to be.

1502
00:54:49,040 --> 00:54:49,880
Okay.

1503
00:54:49,880 --> 00:54:52,280
Now there's a level of true value.

1504
00:54:52,280 --> 00:54:53,680
Okay.

1505
00:54:53,680 --> 00:54:56,560
Then the second question is on that tags

1506
00:54:56,560 --> 00:54:58,840
of filtering the workspaces.

1507
00:54:58,840 --> 00:55:00,640
Will we bring that in premium?

1508
00:55:00,640 --> 00:55:01,480
Tags.

1509
00:55:01,480 --> 00:55:02,320
Yeah, tags.

1510
00:55:02,320 --> 00:55:03,160
Tags.

1511
00:55:03,160 --> 00:55:04,000
Tag, tag.

1512
00:55:04,000 --> 00:55:04,840
Tag, tag.

1513
00:55:04,840 --> 00:55:05,680
Tag.

1514
00:55:05,680 --> 00:55:08,960
Yeah, for a business filter.

1515
00:55:08,960 --> 00:55:09,800
Later on.

1516
00:55:09,800 --> 00:55:12,600
This is also then very similar question.

1517
00:55:12,640 --> 00:55:15,480
Because it will then start making them build

1518
00:55:15,480 --> 00:55:17,680
their listing on our workspaces.

1519
00:55:17,680 --> 00:55:18,520
Right.

1520
00:55:19,400 --> 00:55:21,560
Some, it'll be functional.

1521
00:55:21,560 --> 00:55:25,120
Like Bob Bala did that PO type, GRN type.

1522
00:55:25,120 --> 00:55:26,600
That could be part of.

1523
00:55:26,600 --> 00:55:27,600
Tracking.

1524
00:55:27,600 --> 00:55:29,440
Process, sorting process.

1525
00:55:29,440 --> 00:55:30,960
Yeah, sorting process.

1526
00:55:30,960 --> 00:55:32,080
Okay.

1527
00:55:32,080 --> 00:55:34,120
So we may need that, I think.

1528
00:55:34,120 --> 00:55:35,640
That is one.

1529
00:55:35,640 --> 00:55:38,240
And in general, how should we look at our objects

1530
00:55:38,240 --> 00:55:40,560
like user?

1531
00:55:40,560 --> 00:55:44,680
Even for their capital, the urgency is not there.

1532
00:55:44,680 --> 00:55:47,800
Because a source is completed,

1533
00:55:47,800 --> 00:55:49,440
it is not there.

1534
00:55:49,440 --> 00:55:51,120
It is not a copy of the box object.

1535
00:55:51,120 --> 00:55:54,000
Yeah, if that is our way of looking at things,

1536
00:55:54,000 --> 00:55:54,840
then I think we do.

1537
00:55:54,840 --> 00:55:55,680
It's not critical.

1538
00:55:55,680 --> 00:55:56,520
That's why I asked that question.

1539
00:55:56,520 --> 00:55:57,480
We're just a transaction.

1540
00:55:57,480 --> 00:55:59,480
Yeah, we are just helping to run flows.

1541
00:55:59,480 --> 00:56:03,360
After the product completed, we have no matter.

1542
00:56:03,360 --> 00:56:06,200
So let's say an assignee comes to sign

1543
00:56:06,200 --> 00:56:07,640
a document on our platform.

1544
00:56:07,640 --> 00:56:10,200
We should also then email back the signed document.

1545
00:56:10,200 --> 00:56:12,680
We should not hold anything, right?

1546
00:56:12,680 --> 00:56:13,520
Like DocuSign.

1547
00:56:16,280 --> 00:56:18,400
You should purely assume to dispatch,

1548
00:56:18,400 --> 00:56:21,880
like you should complete the picture in that transaction.

1549
00:56:23,880 --> 00:56:26,240
You don't come back to look up that E-Sign.

1550
00:56:32,240 --> 00:56:34,200
So if there were output based on the transaction,

1551
00:56:34,200 --> 00:56:35,040
we should send.

1552
00:56:38,920 --> 00:56:40,080
Yeah, it's clear.

1553
00:56:41,080 --> 00:56:43,200
Because otherwise, they can come to our system

1554
00:56:43,200 --> 00:56:45,560
to look up which one you signed, when you signed.

1555
00:56:45,560 --> 00:56:47,120
When you signed.

1556
00:56:47,120 --> 00:56:49,240
Yeah, yeah, yeah.

1557
00:56:49,240 --> 00:56:50,800
Yeah.

1558
00:56:50,800 --> 00:56:51,640
Got you.

1559
00:56:51,640 --> 00:56:53,320
Yeah.

1560
00:56:53,320 --> 00:56:54,840
So it sounds like,

1561
00:56:57,040 --> 00:56:58,720
it's all written up.

1562
00:56:58,720 --> 00:57:01,720
So let's see, it sounds like it is.

