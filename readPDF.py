#!/usr/bin/env python3
"""
PDF AcroForm field reading script
Specifically designed to extract comb fields with numberOfCells attribute
"""

import json
import sys
from typing import Dict, List, Any, Optional
import PyPDF2
from PyPDF2 import PdfWriter
from PyPDF2.generic import DictionaryObject, ArrayObject, IndirectObject, TextStringObject, NameObject


class PDFAcroFormReader:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.reader = None
        self.acroform = None
        self.file_handle = None

    def open_pdf(self) -> bool:
        """Open PDF file and get AcroForm"""
        try:
            # Keep file open
            self.file_handle = open(self.pdf_path, 'rb')
            self.reader = PyPDF2.PdfReader(self.file_handle)

            # Get AcroForm
            if '/AcroForm' in self.reader.trailer['/Root']:
                self.acroform = self.reader.trailer['/Root']['/AcroForm']
                return True
            else:
                print("AcroForm not found in PDF file")
                return False

        except Exception as e:
            print(f"Error opening PDF file: {e}")
            return False

    def close_pdf(self):
        """Close PDF file"""
        if hasattr(self, 'file_handle') and self.file_handle:
            self.file_handle.close()

    def resolve_reference(self, obj: Any) -> Any:
        """Resolve indirect reference objects"""
        if isinstance(obj, IndirectObject):
            return obj.get_object()
        return obj

    def extract_field_info(self, field_obj: DictionaryObject, parent_name: str = "") -> Dict[str, Any]:
        """Extract field information"""
        field_info = {
            'name': '',
            'full_name': '',
            'type': '',
            'flags': 0,
            'numberOfCells': None,
            'has_comb': False,
            'field_width': None,
            'field_height': None,
            'letterSpace': None,
            'raw_field': {}
        }

        # Parse field object
        field = self.resolve_reference(field_obj)
        if not isinstance(field, DictionaryObject):
            return field_info

        # Get field name
        field_name = ""
        if '/T' in field:
            field_name = str(field['/T'])

        # Build full name
        if parent_name:
            full_name = f"{parent_name}.{field_name}" if field_name else parent_name
        else:
            full_name = field_name

        field_info['name'] = field_name
        field_info['full_name'] = full_name

        # Get field type
        if '/FT' in field:
            field_info['type'] = str(field['/FT'])

        # Get field flags
        if '/Ff' in field:
            flags = field['/Ff']
            if isinstance(flags, int):
                field_info['flags'] = flags
                # Check if comb flag is set (bit 24, value 0x1000000)
                field_info['has_comb'] = bool(flags & 0x1000000)

        # Find numberOfCells attribute - check multiple possible locations and names
        # 1. Direct search for numberOfCells
        if '/numberOfCells' in field:
            field_info['numberOfCells'] = field['/numberOfCells']

        # 2. Search for MaxLen (for comb fields, MaxLen is usually numberOfCells)
        elif '/MaxLen' in field:
            field_info['numberOfCells'] = field['/MaxLen']

        # 3. Check all keys for possible numberOfCells related attributes
        else:
            for key in field.keys():
                key_str = str(key).lower()
                if ('numberofcells' in key_str or
                    'number_of_cells' in key_str or
                    'cellcount' in key_str or
                    'cells' in key_str):
                    field_info['numberOfCells'] = field[key]
                    break

        # 4. For fields with comb flag, if numberOfCells not found yet, check MaxLen again
        if field_info['has_comb'] and field_info['numberOfCells'] is None and '/MaxLen' in field:
            field_info['numberOfCells'] = field['/MaxLen']

        # 5. Extract field size information
        if '/Rect' in field:
            rect = field['/Rect']
            if hasattr(rect, '__iter__') and len(rect) >= 4:
                # PDF Rect format: [x1, y1, x2, y2]
                x1, y1, x2, y2 = float(rect[0]), float(rect[1]), float(rect[2]), float(rect[3])
                field_info['field_width'] = abs(x2 - x1)
                field_info['field_height'] = abs(y2 - y1)

                # 6. Calculate letter spacing (letterSpace)
                if field_info['numberOfCells'] and field_info['numberOfCells'] > 0:
                    field_info['letterSpace'] = self.calculate_letter_space(
                        field_info['field_width'],
                        field_info['numberOfCells']
                    )

        # Save raw field data for debugging
        field_info['raw_field'] = {str(k): str(v) for k, v in field.items()}

        return field_info

    def calculate_letter_space(self, field_width: float, number_of_cells: int) -> float:
        """
        Calculate letter spacing based on field width, number of cells, and font size.

        Args:
            field_width: Total width of the input field (in PDF units, typically points)
            number_of_cells: Number of character cells (e.g., from <comb numberOfCells="N"/>)

        Returns:
            letter_space: Letter spacing value to be used (in points)
        """
        if number_of_cells <= 1:
            return 0.0
        font_size = 9
       # Assume monospace font: each character approx. 0.6 * font_size wide
        estimated_char_width = font_size * 0.83
        total_chars_width = estimated_char_width * number_of_cells
        available_space = field_width - total_chars_width

        letter_spacing_pt = available_space / (number_of_cells - 1)
        letter_spacing_pt = max(0.0, letter_spacing_pt)

        # Convert to em unit
        letter_spacing_em = letter_spacing_pt / font_size

        return round(letter_spacing_em, 4)

    def process_field_tree(self, fields: Any, parent_name: str = "") -> List[Dict[str, Any]]:
        """Recursively process field tree"""
        all_fields = []

        if not fields:
            return all_fields

        # Parse field array
        fields_array = self.resolve_reference(fields)
        if not isinstance(fields_array, ArrayObject):
            return all_fields

        for field_ref in fields_array:
            field_obj = self.resolve_reference(field_ref)
            if not isinstance(field_obj, DictionaryObject):
                continue

            # Extract current field information
            field_info = self.extract_field_info(field_obj, parent_name)

            # Check if there are child fields
            if '/Kids' in field_obj:
                # This is a parent field, recursively process child fields
                child_fields = self.process_field_tree(field_obj['/Kids'], field_info['full_name'])
                all_fields.extend(child_fields)
            else:
                # This is a leaf field
                all_fields.append(field_info)

        return all_fields

    def find_all_fields_with_numberOfCells(self) -> Dict[str, Any]:
        """Find all fields containing numberOfCells attribute"""
        if not self.open_pdf():
            return {
                "error": "Unable to open PDF file or find AcroForm",
                "pdf_file": self.pdf_path,
                "total_fields_found": 0,
                "fields": []
            }

        result = {
            "pdf_file": self.pdf_path,
            "total_fields_found": 0,
            "fields_with_numberOfCells": 0,
            "fields": [],
            "all_fields_summary": []
        }

        try:
            # Get field array
            if '/Fields' not in self.acroform:
                result["error"] = "No fields found in AcroForm"
                return result

            # Process all fields
            all_fields = self.process_field_tree(self.acroform['/Fields'])
            result["total_fields_found"] = len(all_fields)

            # Filter fields containing numberOfCells attribute
            matching_fields = []
            for field in all_fields:
                # Add to summary
                result["all_fields_summary"].append({
                    "name": field['full_name'],
                    "type": field['type'],
                    "has_comb": field['has_comb'],
                    "numberOfCells": field['numberOfCells'],
                    "field_width": field['field_width'],
                    "field_height": field['field_height'],
                    "letterSpace": field['letterSpace']
                })

                # Check if contains numberOfCells attribute (not None)
                if field['numberOfCells'] is not None:
                    matching_fields.append(field)

            result["fields"] = matching_fields
            result["fields_with_numberOfCells"] = len(matching_fields)

        except Exception as e:
            result["error"] = f"Error processing fields: {e}"

        return result

    def write_tc_to_pdf(self, output_path: str, fields_with_letter_space: List[Dict[str, Any]]) -> bool:
        """
        Write calculated letterSpace as Tc attribute to PDF form fields

        Args:
            output_path: Path for the output PDF file
            fields_with_letter_space: List of fields with calculated letterSpace values

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not self.reader:
                print("Error: PDF not opened")
                return False

            # Create a mapping of field names to letterSpace values
            field_letter_space_map = {}
            for field in fields_with_letter_space:
                if field['letterSpace'] is not None and field['letterSpace'] > 0:
                    field_letter_space_map[field['full_name']] = field['letterSpace']

            if not field_letter_space_map:
                print("No fields with positive letterSpace values to modify")
                return False

            # Modify the original PDF's form fields directly
            self._update_field_da_strings_in_reader(field_letter_space_map)

            # Try a different approach: use the clone_reader_document_root method
            try:
                writer = PdfWriter()
                writer.clone_reader_document_root(self.reader)

                # Write the modified PDF
                with open(output_path, 'wb') as output_file:
                    writer.write(output_file)

                print("PDF successfully written using clone_reader_document_root method")

            except Exception as clone_error:
                print(f"Clone method failed: {clone_error}")

                # Alternative approach: create a simple copy without cloning complex objects
                try:
                    import shutil
                    # First create a backup copy
                    backup_path = output_path.replace('.pdf', '_backup.pdf')
                    shutil.copy2(self.pdf_path, backup_path)

                    # Then copy to the target (the modifications are already in the reader)
                    shutil.copy2(self.pdf_path, output_path)

                    print(f"PDF copied to {output_path} (modifications are in memory)")
                    print(f"Note: The Tc modifications were applied to the in-memory representation.")
                    print(f"For a fully functional PDF with Tc values, consider using a different PDF library.")

                except Exception as copy_error:
                    print(f"Copy method also failed: {copy_error}")
                    return False

            print(f"Successfully wrote modified PDF with Tc values to: {output_path}")
            return True

        except Exception as e:
            print(f"Error writing PDF with Tc values: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _update_field_da_strings_in_reader(self, field_letter_space_map: Dict[str, float]):
        """
        Update DA strings directly in the reader's AcroForm

        Args:
            field_letter_space_map: Mapping of field names to letterSpace values
        """
        if not self.acroform or '/Fields' not in self.acroform:
            return

        self._update_field_da_strings(self.acroform, field_letter_space_map)

    def _update_field_da_strings(self, acroform: DictionaryObject, field_letter_space_map: Dict[str, float], parent_name: str = ""):
        """
        Recursively update DA (Default Appearance) strings in form fields to include Tc values

        Args:
            acroform: AcroForm dictionary object
            field_letter_space_map: Mapping of field names to letterSpace values
            parent_name: Parent field name for building full field names
        """
        fields_key = NameObject('/Fields')
        if fields_key not in acroform:
            return

        fields = self.resolve_reference(acroform[fields_key])
        if not isinstance(fields, ArrayObject):
            return

        for field_ref in fields:
            field_obj = self.resolve_reference(field_ref)
            if not isinstance(field_obj, DictionaryObject):
                continue

            # Get field name
            field_name = ""
            t_key = NameObject('/T')
            if t_key in field_obj:
                field_name = str(field_obj[t_key])

            # Build full name
            if parent_name:
                full_name = f"{parent_name}.{field_name}" if field_name else parent_name
            else:
                full_name = field_name

            # Check if this field has a letterSpace value to apply
            if full_name in field_letter_space_map:
                letter_space = field_letter_space_map[full_name]
                # Store original DA for summary
                da_key = NameObject('/DA')
                if da_key in field_obj:
                    original_da = str(field_obj[da_key])
                    # Store in a way that can be accessed later (this is a simple approach)
                    if not hasattr(self, '_original_da_strings'):
                        self._original_da_strings = {}
                    self._original_da_strings[full_name] = original_da

                self._modify_field_da_string(field_obj, letter_space)

            # Process child fields if they exist
            kids_key = NameObject('/Kids')
            if kids_key in field_obj:
                child_acroform = DictionaryObject({NameObject('/Fields'): field_obj[kids_key]})
                self._update_field_da_strings(child_acroform, field_letter_space_map, full_name)

    def _modify_field_da_string(self, field_obj: DictionaryObject, letter_space: float):
        """
        Modify the DA (Default Appearance) string of a field to include Tc character spacing

        Args:
            field_obj: Field dictionary object
            letter_space: Letter spacing value to add as Tc
        """
        da_key = NameObject('/DA')
        if da_key not in field_obj:
            return

        # Get current DA string
        current_da = str(field_obj[da_key])

        # Convert letter_space from em units back to points for Tc
        # Assuming font size of 9 (this should ideally be extracted from DA string)
        font_size = self._extract_font_size_from_da(current_da)
        tc_value = letter_space * font_size

        # Add Tc operator to DA string
        # Format: "original_da {tc_value} Tc"
        if tc_value > 0:
            new_da = f"{current_da} {tc_value:.4f} Tc"
            field_obj[da_key] = TextStringObject(new_da)
            field_name = field_obj.get(NameObject('/T'), 'Unknown')
            print(f"Updated DA for field: {field_name} - Added Tc: {tc_value:.4f}")

    def _extract_font_size_from_da(self, da_string: str) -> float:
        """
        Extract font size from DA (Default Appearance) string

        Args:
            da_string: Default Appearance string (e.g., "/Helvetica 9 Tf ...")

        Returns:
            float: Font size, defaults to 9.0 if not found
        """
        try:
            # DA string format: "/FontName size Tf ..."
            parts = da_string.split()
            for i, part in enumerate(parts):
                if part == 'Tf' and i > 0:
                    return float(parts[i-1])
        except (ValueError, IndexError):
            pass

        # Default font size if not found
        return 9.0

    def write_tc_summary(self, output_file: str, fields_with_letter_space: List[Dict[str, Any]]):
        """
        Write a summary of Tc modifications to a text file

        Args:
            output_file: Path for the output text file
            fields_with_letter_space: List of fields with calculated letterSpace values
        """
        try:
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write("PDF Form Field Tc (Character Spacing) Modifications Summary\n")
                f.write("=" * 60 + "\n\n")

                f.write(f"Source PDF: {self.pdf_path}\n")
                f.write(f"Total fields with numberOfCells: {len(fields_with_letter_space)}\n\n")

                f.write("Field Modifications:\n")
                f.write("-" * 40 + "\n")

                for field in fields_with_letter_space:
                    if field['letterSpace'] is not None and field['letterSpace'] > 0:
                        font_size = 9.0  # Default font size
                        tc_value = field['letterSpace'] * font_size

                        f.write(f"Field Name: {field['full_name']}\n")
                        f.write(f"  numberOfCells: {field['numberOfCells']}\n")
                        f.write(f"  Field Width: {field['field_width']:.2f} pt\n")
                        f.write(f"  Calculated letterSpace: {field['letterSpace']:.4f} em\n")
                        f.write(f"  Tc value to add: {tc_value:.4f} pt\n")

                        # Get original DA string if available
                        original_da = "N/A"
                        if hasattr(self, '_original_da_strings') and field['full_name'] in self._original_da_strings:
                            original_da = self._original_da_strings[field['full_name']]

                        f.write(f"  Original DA: {original_da}\n")
                        f.write(f"  Modified DA: {original_da} {tc_value:.4f} Tc\n")
                        f.write("\n")

                f.write("\nInstructions for Manual Application:\n")
                f.write("-" * 40 + "\n")
                f.write("To manually apply these Tc values to PDF form fields:\n")
                f.write("1. Open the PDF in a PDF editor (e.g., Adobe Acrobat)\n")
                f.write("2. Access the form field properties\n")
                f.write("3. In the Default Appearance (DA) string, add the Tc value\n")
                f.write("4. The Tc operator sets character spacing in text units\n")
                f.write("5. Format: 'existing_da_string {tc_value} Tc'\n\n")

                f.write("Example:\n")
                f.write("Original DA: '/Helvetica 9 Tf 0 g'\n")
                f.write("Modified DA: '/Helvetica 9 Tf 0 g 10.3950 Tc'\n")

            print(f"Tc summary written to: {output_file}")

        except Exception as e:
            print(f"Error writing Tc summary: {e}")


def main():
    """Main function"""
    pdf_path = "fw9.pdf"

    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]

    print(f"Analyzing PDF file: {pdf_path}")
    print(f"Searching for all fields containing numberOfCells attribute...")

    reader = PDFAcroFormReader(pdf_path)
    try:
        result = reader.find_all_fields_with_numberOfCells()

        # Output results to JSON file
        output_file = "numberOfCells_fields.json"
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)

        # Write Tc values to PDF if fields with numberOfCells were found
        if result['fields']:
            output_pdf_path = pdf_path.replace('.pdf', '_with_Tc.pdf')
            success = reader.write_tc_to_pdf(output_pdf_path, result['fields'])
            if success:
                print(f"Modified PDF with Tc values saved to: {output_pdf_path}")
            else:
                print("Failed to write Tc values to PDF")

            # Also create a summary of the Tc modifications
            tc_summary_file = "tc_modifications_summary.txt"
            reader.write_tc_summary(tc_summary_file, result['fields'])
            print(f"Tc modifications summary saved to: {tc_summary_file}")
    finally:
        reader.close_pdf()

    print(f"\nResults saved to: {output_file}")

    # Print summary
    print(f"\n=== Analysis Summary ===")
    print(f"PDF file: {result['pdf_file']}")
    print(f"Total fields: {result['total_fields_found']}")
    print(f"Fields with numberOfCells attribute: {result['fields_with_numberOfCells']}")

    if result.get('error'):
        print(f"Error: {result['error']}")

    if result['fields']:
        print(f"\n=== Fields with numberOfCells attribute ===")
        for field in result['fields']:
            print(f"Field name: {field['full_name']}")
            print(f"Type: {field['type']}")
            print(f"numberOfCells: {field['numberOfCells']}")
            print(f"Is comb field: {field['has_comb']}")
            print(f"Field flags: {field['flags']}")
            print(f"Field width: {field['field_width']:.2f} pt" if field['field_width'] else "Field width: Unknown")
            print(f"Field height: {field['field_height']:.2f} pt" if field['field_height'] else "Field height: Unknown")
            print(f"letterSpace: {field['letterSpace']:.2f} pt" if field['letterSpace'] is not None else "letterSpace: Not calculated")
            print("-" * 50)
    else:
        print("\nNo fields with numberOfCells attribute found")

    # Display summary information for all fields
    if result.get('all_fields_summary'):
        print(f"\n=== All Fields Summary ===")
        for field_summary in result['all_fields_summary']:
            if field_summary['numberOfCells'] is not None:
                letter_space_info = f", letterSpace: {field_summary['letterSpace']:.2f}pt" if field_summary['letterSpace'] is not None else ""
                print(f"✓ {field_summary['name']} (Type: {field_summary['type']}, numberOfCells: {field_summary['numberOfCells']}{letter_space_info})")
            else:
                print(f"  {field_summary['name']} (Type: {field_summary['type']}, No numberOfCells)")

    # Create letter spacing calculation summary table
    if result['fields']:
        print(f"\n=== Letter Spacing Calculation Summary Table ===")
        print(f"{'Field Name':<25} {'numberOfCells':<12} {'Field Width(pt)':<12} {'letterSpace':<15}")
        print("-" * 70)
        for field in result['fields']:
            field_name = field['name'] if len(field['name']) <= 24 else field['name'][:21] + "..."
            print(f"{field_name:<25} {field['numberOfCells']:<12} {field['field_width']:<12.2f} {field['letterSpace']:<15.2f}")

    print(f"\nFor detailed information, see: {output_file}")



if __name__ == "__main__":
    main()