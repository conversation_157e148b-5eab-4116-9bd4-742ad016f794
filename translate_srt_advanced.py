#!/usr/bin/env python3
"""
高级SRT字幕翻译脚本
支持多种翻译服务和批量处理
"""

import re
import sys
import time
import requests
from typing import List, Tuple
import json

class SRTTranslator:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
    
    def parse_srt_file(self, file_path: str) -> List[Tuple[str, str, str]]:
        """解析SRT文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 分割字幕块
        blocks = re.split(r'\n\s*\n', content.strip())
        subtitles = []
        
        for block in blocks:
            lines = block.strip().split('\n')
            if len(lines) >= 3:
                number = lines[0]
                timestamp = lines[1]
                text = '\n'.join(lines[2:])
                subtitles.append((number, timestamp, text))
        
        return subtitles
    
    def translate_with_google(self, text: str, target_lang: str = 'zh') -> str:
        """使用Google翻译（免费版本）"""
        try:
            url = "https://translate.googleapis.com/translate_a/single"
            params = {
                'client': 'gtx',
                'sl': 'en',
                'tl': target_lang,
                'dt': 't',
                'q': text
            }
            
            response = self.session.get(url, params=params, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result and result[0]:
                    translated = ''.join([item[0] for item in result[0] if item[0]])
                    return translated
            return text
        except Exception as e:
            print(f"翻译错误: {e}")
            return text
    
    def translate_with_baidu(self, text: str) -> str:
        """使用百度翻译（需要API密钥）"""
        # 这里需要百度翻译API的实现
        # 需要注册百度翻译API并获取密钥
        return text
    
    def is_english_text(self, text: str) -> bool:
        """检查文本是否包含英文"""
        return bool(re.search(r'[a-zA-Z]', text))
    
    def translate_text(self, text: str, method: str = 'google') -> str:
        """翻译文本"""
        if not self.is_english_text(text):
            return text
        
        if method == 'google':
            return self.translate_with_google(text)
        elif method == 'baidu':
            return self.translate_with_baidu(text)
        else:
            return text
    
    def translate_srt(self, input_file: str, output_file: str, method: str = 'google'):
        """翻译整个SRT文件"""
        print(f"正在解析文件: {input_file}")
        subtitles = self.parse_srt_file(input_file)
        
        print(f"找到 {len(subtitles)} 个字幕条目")
        print("正在翻译...")
        
        translated_subtitles = []
        for i, (number, timestamp, text) in enumerate(subtitles):
            print(f"翻译进度: {i+1}/{len(subtitles)}", end='\r')
            
            translated_text = self.translate_text(text, method)
            translated_subtitles.append((number, timestamp, translated_text))
            
            # 添加延迟避免请求过于频繁
            time.sleep(0.1)
        
        print(f"\n正在写入文件: {output_file}")
        self.write_srt_file(translated_subtitles, output_file)
        print("翻译完成！")
    
    def write_srt_file(self, subtitles: List[Tuple[str, str, str]], output_path: str):
        """写入SRT文件"""
        with open(output_path, 'w', encoding='utf-8') as f:
            for i, (number, timestamp, text) in enumerate(subtitles):
                f.write(f"{number}\n")
                f.write(f"{timestamp}\n")
                f.write(f"{text}\n")
                if i < len(subtitles) - 1:
                    f.write("\n")

def main():
    if len(sys.argv) < 3:
        print("使用方法: python translate_srt_advanced.py <输入文件> <输出文件> [翻译方法]")
        print("翻译方法: google (默认), baidu")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    method = sys.argv[3] if len(sys.argv) > 3 else 'google'
    
    translator = SRTTranslator()
    translator.translate_srt(input_file, output_file, method)

if __name__ == "__main__":
    main()
