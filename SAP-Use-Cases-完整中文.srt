1
00:00:00,000 --> 00:00:24,800
然后这个项目列表将被锁定。

2
00:00:24,800 --> 00:00:27,720
因为你输入了这个，然后我们知道什么是pm。

3
00:00:28,360 --> 00:00:32,000
没有这个，这是空的。

4
00:00:32,000 --> 00:00:35,440
所以这个输入将触发这个习惯。

5
00:00:35,440 --> 00:00:43,400
然后在这个下拉菜单中，你也只需选择这个项目。

6
00:00:43,400 --> 00:00:45,440
你可以选择多个项目。

7
00:00:45,440 --> 00:00:47,680
你的意思是选择多个。

8
00:00:47,680 --> 00:00:57,640
你的意思是你可能收到数量三。

9
00:00:57,640 --> 00:00:59,440
你可能收到二，收到三。

10
00:00:59,440 --> 00:01:03,080
然后三，你收到四。

11
00:01:03,080 --> 00:01:05,880
所以这有点像一个-

12
00:01:05,880 --> 00:01:09,240
不，但我们可以将其映射到表单的不同部分。

13
00:01:09,240 --> 00:01:11,440
今天，我们如何进行扫描是

14
00:01:11,440 --> 00:01:14,320
我们有重复的部分。

15
00:01:14,320 --> 00:01:19,320
但这种动态方式很难-

16
00:01:19,320 --> 00:01:21,520
不，我们可以一次选择一个项目。

17
00:01:21,520 --> 00:01:22,240
那没关系。

18
00:01:22,240 --> 00:01:24,360
是的，所以这意味着如果你选择一个，

19
00:01:24,360 --> 00:01:27,680
你会自动创建另一个项目。

20
00:01:27,680 --> 00:01:28,880
如果你想选择，你就选择。

21
00:01:28,880 --> 00:01:30,440
如果你不选择，那就出去了。

22
00:01:30,440 --> 00:01:31,240
是的，是的，是的，是的。

23
00:01:31,240 --> 00:01:32,480
是的，你完成了。

24
00:01:32,480 --> 00:01:35,960
另一种方法是给你一个表格。

25
00:01:35,960 --> 00:01:37,640
是的。

26
00:01:37,640 --> 00:01:40,520
然后因为选择了这个，显示这个表格。

27
00:01:40,520 --> 00:01:44,040
项目一，项目二，项目三。

28
00:01:44,040 --> 00:01:46,800
然后你只需在这里填写数字。

29
00:01:46,800 --> 00:01:49,800
是的，是的，是的，是的。

30
00:01:49,800 --> 00:01:51,800
斯坦利，有一个评论。

31
00:01:51,800 --> 00:01:55,040
表格，通常，体验不是很好，

32
00:01:55,040 --> 00:01:57,720
特别是在较小的设备上，对吧？

33
00:01:57,720 --> 00:02:03,360
所以人们解决这个问题的另一种方法是你将其视为一个-

34
00:02:03,360 --> 00:02:06,360
对，你为一个部分带来一个新的控制台，像页面一样。

35
00:02:06,360 --> 00:02:08,360
这是页面的较小版本。

36
00:02:08,360 --> 00:02:10,200
你可以有任意多的字段。

37
00:02:10,200 --> 00:02:11,600
这里会有一个添加按钮。

38
00:02:11,600 --> 00:02:12,720
所以它会不断重复。

39
00:02:12,720 --> 00:02:14,120
是的，是的。

40
00:02:14,120 --> 00:02:15,520
更用户友好。

41
00:02:15,520 --> 00:02:16,120
和移动端。

42
00:02:16,120 --> 00:02:16,920
是的，是的。

43
00:02:16,920 --> 00:02:18,400
我们可以做到。

44
00:02:18,400 --> 00:02:21,480
因为这意味着在错误的时间，这个表单在那里。

45
00:02:21,480 --> 00:02:22,480
你可以。

46
00:02:22,480 --> 00:02:23,960
这是一个错误时间表单。

47
00:02:23,960 --> 00:02:26,360
是的，这就是我的想法。

48
00:02:26,360 --> 00:02:32,440
这里的关键是关于这个输入导致这个部分。

49
00:02:32,440 --> 00:02:33,920
是的。

50
00:02:33,920 --> 00:02:36,960
然后你有错误的时间。

51
00:02:36,960 --> 00:02:38,720
这是一个非常好的事情。

52
00:02:38,760 --> 00:02:40,640
那是一个用户只会添加。

53
00:02:40,640 --> 00:02:41,120
他会添加。

54
00:02:41,120 --> 00:02:42,560
再次，他会选择新项目，对吧？

55
00:02:42,560 --> 00:02:43,560
是的，是的，是的。

56
00:02:43,560 --> 00:02:44,440
这是错误的时间。

57
00:02:44,440 --> 00:02:47,000
是的，我们的工作在那个项目填充中结束。

58
00:02:47,000 --> 00:02:49,880
再买一个，请。

59
00:02:49,880 --> 00:02:51,640
是的，这听起来像一年的交易。

60
00:02:51,640 --> 00:02:52,520
是的，是的。

61
00:02:52,520 --> 00:02:54,720
好吧，我还有一个问题。

62
00:02:54,720 --> 00:02:56,920
这可能不只是一个已经下拉的项目。

63
00:02:56,920 --> 00:02:59,400
你可能需要为我一个项目。

64
00:02:59,400 --> 00:03:00,040
是的，是的，是的。

65
00:03:00,040 --> 00:03:00,520
正确。

66
00:03:00,520 --> 00:03:01,440
所以这是一个数组，对吧？

67
00:03:01,440 --> 00:03:02,560
不只是一个返回。

68
00:03:02,560 --> 00:03:03,120
正确，正确。

69
00:03:03,120 --> 00:03:08,440
所以对于这个项目，我们需要一个新的行项目，对吧？

70
00:03:09,440 --> 00:03:14,160
好的，让我说说我们今天是如何建模的，对吧？

71
00:03:14,160 --> 00:03:22,240
所以po行项目有大约10个字段。

72
00:03:22,240 --> 00:03:25,520
父级是po文档，对吧？，对吧？

73
00:03:25,520 --> 00:03:28,720
所以这是po号码，对吧？，对吧？

74
00:03:28,720 --> 00:03:31,240
这有，比如说，一些项目。

75
00:03:31,240 --> 00:03:33,240
所以这里有一些字段，这里有一些字段。

76
00:03:33,240 --> 00:03:35,920
但我们将所有这些字段都放入这个。

77
00:03:35,920 --> 00:03:36,920
你理解了，对吧？

78
00:03:36,960 --> 00:03:41,120
所以我们得到采购订单。

79
00:03:41,120 --> 00:03:44,640
假设采购订单有五个项目，对吧？

80
00:03:44,640 --> 00:03:49,120
我们在表中创建五行表，对吧？

81
00:03:49,120 --> 00:03:50,960
采购订单行项目表。

82
00:03:50,960 --> 00:03:52,840
我们创建五条记录。

83
00:03:52,840 --> 00:03:54,440
这就是这个部分的全部内容。

84
00:03:54,440 --> 00:03:58,600
但我们会重复来自标题的信息。

85
00:03:58,600 --> 00:04:00,120
是的，所以有一个问题，巴拉。

86
00:04:00,120 --> 00:04:01,840
是的。

87
00:04:01,840 --> 00:04:04,080
不确定我理解。

88
00:04:04,080 --> 00:04:05,520
让我给你展示一下。

89
00:04:05,520 --> 00:04:09,400
也许我们可以做一个演示，然后回到这个模型。

90
00:04:09,400 --> 00:04:11,720
这将显示数据库本身，

91
00:04:11,720 --> 00:04:12,920
我们是如何设计它们的，对吧？

92
00:04:12,920 --> 00:04:14,120
先做一个演示。

93
00:04:14,120 --> 00:04:15,600
好的，好的。

94
00:04:15,600 --> 00:04:17,920
这将有助于理解。

95
00:04:17,920 --> 00:04:20,280
你也可以分享屏幕，巴拉。

96
00:04:20,280 --> 00:04:22,000
分享屏幕，巴拉。

97
00:04:22,000 --> 00:04:25,720
如果你可以连接到那个。

98
00:04:25,720 --> 00:04:26,200
伦敦。

99
00:04:33,160 --> 00:04:35,440
谢谢你，巴拉。

100
00:04:36,440 --> 00:04:37,920
轮椅停了。

101
00:04:43,400 --> 00:04:44,400
是的，它应该工作。

102
00:04:44,400 --> 00:04:45,880
是的，现在很好。

103
00:04:45,880 --> 00:04:47,360
是的。

104
00:04:47,360 --> 00:04:48,840
现在你看到了，不是吗？

105
00:04:48,840 --> 00:04:52,080
是的，一切都在工作。

106
00:04:52,080 --> 00:04:58,400
所以让我们，好的，sap方面展示我的意思。

107
00:04:58,400 --> 00:05:00,080
我会跳过采购申请。

108
00:05:00,080 --> 00:05:00,960
那没关系，对吧？

109
00:05:00,960 --> 00:05:02,720
采购申请你想要通过吗？

110
00:05:02,720 --> 00:05:05,120
是的，也许我们可以切断整个队列。

111
00:05:05,600 --> 00:05:08,960
所以它从pr开始，对吧？，对吧？

112
00:05:08,960 --> 00:05:10,320
它会进入菜单。

113
00:05:10,320 --> 00:05:13,080
是的。

114
00:05:13,080 --> 00:05:17,160
采购申请将是创建po的过程。

115
00:05:17,160 --> 00:05:19,400
是的，它将是初步文档。

116
00:05:19,400 --> 00:05:21,000
你想在汁液中创建po 吗？

117
00:05:21,000 --> 00:05:21,800
不是直接的。

118
00:05:21,800 --> 00:05:25,640
所以会发生的是可能有多个pr。

119
00:05:25,640 --> 00:05:28,400
不，sap中的采购请求对象。

120
00:05:28,400 --> 00:05:28,880
哦，好的。

121
00:05:28,880 --> 00:05:30,200
所以采购需要。

122
00:05:30,200 --> 00:05:33,440
是的，sap中创建一个采购请求对象。

123
00:05:33,520 --> 00:05:36,960
现在，采购人员可以整合采购请求

124
00:05:36,960 --> 00:05:40,840
来自不同部门，如果他说，

125
00:05:40,840 --> 00:05:44,800
我要把所有这些放入一个采购订单，

126
00:05:44,800 --> 00:05:47,800
在供应商方面，你可以这样做。

127
00:05:47,800 --> 00:05:52,040
所以在这里我会审查它并加载表单。

128
00:05:54,680 --> 00:05:57,200
所以这只不过是哪个部门

129
00:05:57,200 --> 00:05:58,560
我在为其工作，对吧？

130
00:05:58,560 --> 00:06:00,400
这是哪个部门？

131
00:06:00,400 --> 00:06:02,040
这实际上来自汁液。

132
00:06:02,040 --> 00:06:04,320
理想情况下，sap。

133
00:06:04,320 --> 00:06:08,760
这也是来自汁液的主数据。

134
00:06:08,760 --> 00:06:11,120
实际上有两个字段我们合并为一个，

135
00:06:11,120 --> 00:06:12,840
这里是一样的。

136
00:06:12,840 --> 00:06:16,800
一个是该部门的代码。

137
00:06:16,800 --> 00:06:18,280
还有描述。

138
00:06:18,280 --> 00:06:19,720
对我们来说，没有智能，对吧？

139
00:06:19,720 --> 00:06:21,840
这都是我们如何获得可以显示的数据，对吧？

140
00:06:21,840 --> 00:06:22,320
是的。

141
00:06:22,320 --> 00:06:23,760
这更多是为了让这个人理解。

142
00:06:23,760 --> 00:06:24,760
正确。

143
00:06:24,760 --> 00:06:27,760
对于第二个，他们可以依赖第一个。

144
00:06:27,760 --> 00:06:29,800
是的，是的，是的。

145
00:06:29,800 --> 00:06:30,560
是的。

146
00:06:30,560 --> 00:06:31,320
不，不，不，不。

147
00:06:31,320 --> 00:06:32,640
供应不依赖于那个。

148
00:06:32,640 --> 00:06:33,280
对不起，先生。

149
00:06:33,280 --> 00:06:33,840
独立的。

150
00:06:33,840 --> 00:06:34,480
独立的。

151
00:06:34,480 --> 00:06:37,680
因为它应该列出这个被获得了。

152
00:06:37,680 --> 00:06:38,520
不，不，不。

153
00:06:38,520 --> 00:06:41,440
对于这个用例来说，那不存在。

154
00:06:41,440 --> 00:06:43,880
你可能在其他用例中有那个。

155
00:06:43,880 --> 00:06:46,400
Erp中，这很常见。，这很常见。

156
00:06:46,400 --> 00:06:47,160
是的。

157
00:06:47,160 --> 00:06:49,640
所以假设我选择一个日期。

158
00:06:49,640 --> 00:06:51,120
这都是标准的。

159
00:06:51,120 --> 00:06:53,120
现在，sap。

160
00:06:53,120 --> 00:06:55,480
这个项目代码和项目描述。

161
00:06:55,480 --> 00:06:57,680
所以这个，再次，我们必须连接

162
00:06:57,680 --> 00:07:00,800
因为我们没有依赖的下拉菜单。

163
00:07:00,840 --> 00:07:03,280
斯坦利，对于你说的例子，对吧？，对吧？

164
00:07:03,280 --> 00:07:08,600
理想情况下，应该发生的是S001是第一个下拉菜单。

165
00:07:08,600 --> 00:07:11,160
然后基于那个，第二个描述

166
00:07:11,160 --> 00:07:13,400
会被选择，或者相反。

167
00:07:13,400 --> 00:07:15,000
我可能选择描述。

168
00:07:15,000 --> 00:07:18,920
因为那是，那真的是一对多还是一对一？

169
00:07:18,920 --> 00:07:19,760
只有一对一。

170
00:07:19,760 --> 00:07:20,480
一对一。

171
00:07:20,480 --> 00:07:22,280
这是项目代码和项目描述。

172
00:07:22,280 --> 00:07:24,600
在这种情况下，这是一个安全的方法。

173
00:07:24,600 --> 00:07:27,520
S001上有多个质量

174
00:07:27,520 --> 00:07:30,560
要选择或其他东西，它变得棘手，对吧？

175
00:07:30,800 --> 00:07:31,400
是的，不，不。

176
00:07:31,400 --> 00:07:33,080
在这种情况下，它是一对一的。

177
00:07:33,080 --> 00:07:35,160
实际上，当我们与arihanth

178
00:07:35,160 --> 00:07:39,320
关于那个外部管理的列表时，我们认为一个项目

179
00:07:39,320 --> 00:07:41,280
可以有多个列。

180
00:07:41,280 --> 00:07:45,120
所以在下拉菜单中，你选择哪个是主键，

181
00:07:45,120 --> 00:07:46,600
我的意思是，主列。

182
00:07:46,600 --> 00:07:49,280
但你也可以连接其他列

183
00:07:49,280 --> 00:07:50,640
在下拉菜单中显示。

184
00:07:50,640 --> 00:07:54,120
你可以说，下拉菜单，你显示1，2，3，4，

185
00:07:54,120 --> 00:07:59,120
所有组合在一起，也许用逗号或某种分隔符。

186
00:07:59,120 --> 00:08:02,000
但代码是这个。

187
00:08:02,000 --> 00:08:04,520
这意味着选择了这个代码。

188
00:08:04,520 --> 00:08:06,800
sap时，

189
00:08:06,800 --> 00:08:10,480
我们不需要再次发送项目描述。

190
00:08:10,480 --> 00:08:14,360
这是因为一个人必须理解那是什么部分。

191
00:08:14,360 --> 00:08:16,320
他不能只通过项目代码。

192
00:08:16,320 --> 00:08:18,880
他实际上只会知道描述，

193
00:08:18,880 --> 00:08:20,760
说它是镀锌的。

194
00:08:20,760 --> 00:08:22,560
sap之外这样做吗，

195
00:08:22,560 --> 00:08:24,280
这么多数据在汁液中？

196
00:08:24,280 --> 00:08:26,160
有人会如何这样提出？

197
00:08:26,160 --> 00:08:27,040
他们这样做。

198
00:08:28,000 --> 00:08:30,440
我实际上有Excel。

199
00:08:30,440 --> 00:08:31,720
谁会上传到汁液？

200
00:08:31,720 --> 00:08:32,480
好的。

201
00:08:32,480 --> 00:08:33,600
好问题。

202
00:08:33,600 --> 00:08:35,880
所以现在，在现实世界中发生什么，对吧？

203
00:08:39,640 --> 00:08:42,680
我们实际上有来自他们的数据。

204
00:08:48,080 --> 00:08:50,200
这实际上是他们如何提出的。

205
00:08:50,200 --> 00:08:51,560
所以这是一个简单的表单。

206
00:08:51,560 --> 00:08:52,960
是的。

207
00:08:52,960 --> 00:08:56,040
斯坦利是实际的采购请求表单。

208
00:08:56,080 --> 00:08:59,040
所以这是部门。

209
00:08:59,040 --> 00:09:01,320
他们手动分配号码。

210
00:09:01,320 --> 00:09:06,000
po号码，也是，他们事后记录，对吧？，对吧？

211
00:09:06,000 --> 00:09:09,000
所以采购部门会签名。

212
00:09:09,000 --> 00:09:11,920
他们会输入po号码并签名。

213
00:09:11,920 --> 00:09:13,280
供应商号码，供应商名称。

214
00:09:13,280 --> 00:09:15,440
所以有人会给他们项目的主数据。

215
00:09:15,440 --> 00:09:15,920
正确。

216
00:09:15,920 --> 00:09:16,760
项目描述。

217
00:09:16,760 --> 00:09:18,560
这是手动的。

218
00:09:18,560 --> 00:09:21,840
然后他们会在汁液中手动输入，基本上，基本上，

219
00:09:21,840 --> 00:09:23,120
在批准之后。

220
00:09:23,120 --> 00:09:25,320
即使他们要在这里输入，他们

221
00:09:25,320 --> 00:09:26,600
需要参考一些表格，不是吗？

222
00:09:26,600 --> 00:09:27,120
是的，是的，是的。

223
00:09:27,120 --> 00:09:28,160
他们怎么知道项目是什么？

224
00:09:28,160 --> 00:09:28,800
是的，是的，是的。

225
00:09:28,800 --> 00:09:30,400
他们实际上必须参考一些打印件。

226
00:09:30,400 --> 00:09:31,680
另一个主数据。

227
00:09:31,680 --> 00:09:33,120
是的，是的，是的。

228
00:09:33,120 --> 00:09:36,040
但看，这就是我们实际上在数字化的。

229
00:09:36,040 --> 00:09:38,240
所以我们不能给他们另一个主数据。

230
00:09:38,240 --> 00:09:39,400
硬编码列表，是的。

231
00:09:39,400 --> 00:09:39,920
是的，是的。

232
00:09:39,920 --> 00:09:43,040
所以我的意思是，他们已经发送给我们一切，斯坦利。

233
00:09:43,040 --> 00:09:46,560
看，他们已经发送给我们采购订单，格式。

234
00:09:46,560 --> 00:09:47,120
好的。

235
00:09:47,120 --> 00:09:48,080
他们今天如何做。

236
00:09:48,080 --> 00:09:49,240
那是表格，史丹利。

237
00:09:49,240 --> 00:09:51,200
他说，项目数量。

238
00:09:51,200 --> 00:09:52,800
它可以有任意数量的部分。

239
00:09:52,800 --> 00:09:54,160
是的。

240
00:09:54,200 --> 00:09:57,520
这也是非常可定制的，取决于业务。

241
00:09:57,520 --> 00:09:59,840
他们真正需要的，它有列，对吧？

242
00:09:59,840 --> 00:10:00,640
是的，是的，是的。

243
00:10:00,640 --> 00:10:03,240
我们不能在那里强制任何格式。

244
00:10:03,240 --> 00:10:04,960
所以这是材料请求。

245
00:10:04,960 --> 00:10:06,480
这是材料请求表。

246
00:10:06,480 --> 00:10:07,800
尺寸和规格。

247
00:10:07,800 --> 00:10:10,080
这个和采购请求之间的唯一区别，

248
00:10:10,080 --> 00:10:11,920
这也是一个采购请求，史丹利。

249
00:10:11,920 --> 00:10:15,640
唯一的事情是，这可能来自库存，

250
00:10:15,640 --> 00:10:17,840
而不是成为采购订单。

251
00:10:17,840 --> 00:10:19,720
所以你现在不用担心那个。

252
00:10:19,720 --> 00:10:20,240
好的。

253
00:10:20,240 --> 00:10:21,560
我的意思是，让我们暂时不管那个。

254
00:10:21,560 --> 00:10:24,680
我的意思是，sap中发生的事情，

255
00:10:24,680 --> 00:10:30,120
无论是从库存中给出还是那种添加

256
00:10:30,120 --> 00:10:31,960
在汁液中发生。

257
00:10:31,960 --> 00:10:34,480
所以现在，这都是可选的。

258
00:10:34,480 --> 00:10:38,040
这只是那里的一个镜像，这个表单。

259
00:10:38,040 --> 00:10:42,040
所以我会说，我想要30件。

260
00:10:42,040 --> 00:10:44,600
这就是anu说的，斯坦利。

261
00:10:44,600 --> 00:10:46,680
现在我们通过隐藏逻辑做到了。

262
00:10:46,680 --> 00:10:49,240
我们通过条件逻辑做到了。

263
00:10:49,240 --> 00:10:50,800
条件逻辑，是的。

264
00:10:50,840 --> 00:10:54,440
现在我可以更改第二个项目。

265
00:10:54,440 --> 00:10:59,760
然后我可以在这里输入数量，好的，40。

266
00:10:59,760 --> 00:11:04,480
计量单位也将是汁液数据。

267
00:11:04,480 --> 00:11:07,280
然后我会提交这个。

268
00:11:07,280 --> 00:11:08,720
好的。

269
00:11:08,720 --> 00:11:10,480
现在它去采购部门。

270
00:11:10,480 --> 00:11:12,320
如果你看手动表单，他们必须

271
00:11:12,320 --> 00:11:15,480
批准并填写po号码等等。

272
00:11:15,480 --> 00:11:18,720
现在，他们只想要从这个得到批准。

273
00:11:18,720 --> 00:11:26,280
所以我们说审查，我们就暂停。

274
00:11:29,920 --> 00:11:33,920
这个采购请求被提交了

275
00:11:33,920 --> 00:11:36,360
由请求方，史丹利。

276
00:11:36,360 --> 00:11:37,240
我们做一件事。

277
00:11:37,240 --> 00:11:40,120
我们重命名工作区名称。

278
00:11:40,120 --> 00:11:42,800
因为现在采购请求需要一个参考。

279
00:11:42,800 --> 00:11:45,760
因为如果我想与采购部门谈论

280
00:11:45,760 --> 00:11:48,640
某个请求，对吧，我需要一个参考

281
00:11:48,640 --> 00:11:49,640
号码。

282
00:11:49,640 --> 00:11:52,120
所以我生成了一个参考号码，

283
00:11:52,120 --> 00:11:55,680
这是一个运行序列，并相应地制作工作区名称

284
00:11:55,680 --> 00:11:57,000
。

285
00:11:57,000 --> 00:12:02,520
另外，我们也给一个过滤器。

286
00:12:02,520 --> 00:12:07,040
我的意思是，我们使用工作区标签来说，

287
00:12:07,040 --> 00:12:10,440
我想要过滤采购请求。

288
00:12:10,440 --> 00:12:14,360
我也可以选择日期，mx32。

289
00:12:14,360 --> 00:12:17,680
然后这就是我们现在构建的方式。

290
00:12:17,760 --> 00:12:19,840
显然，有更好的方法来做。

291
00:12:19,840 --> 00:12:22,760
但这就是他们如何识别并快速

292
00:12:22,760 --> 00:12:26,640
到达他们的请求。

293
00:12:26,640 --> 00:12:27,240
两个标签。

294
00:12:27,240 --> 00:12:31,320
一个是分类pr或po或grn。

295
00:12:31,320 --> 00:12:34,280
第二个是参考号码。

296
00:12:34,280 --> 00:12:36,240
参考号码，史丹利。

297
00:12:36,240 --> 00:12:38,240
所以这样，他们可以快速到达他们的po。

298
00:12:38,240 --> 00:12:38,760
是的。

299
00:12:38,760 --> 00:12:40,360
类别列表号码在哪里？

300
00:12:40,360 --> 00:12:41,360
这些是我们的标签。

301
00:12:41,360 --> 00:12:45,720
我们实际上生成它，并将其存储在标签中。

302
00:12:45,720 --> 00:12:46,240
我们生成。

303
00:12:46,240 --> 00:12:47,320
不是作为功能，对吧？

304
00:12:47,360 --> 00:12:48,880
不，不。

305
00:12:48,880 --> 00:12:50,920
它还没有到达汁液。

306
00:12:50,920 --> 00:12:53,760
这只是一个采购请求申请。

307
00:12:53,760 --> 00:12:57,120
它仍然处于草稿模式，等待决定。

308
00:12:57,120 --> 00:12:59,400
现在有人必须决定是否继续前进。

309
00:12:59,400 --> 00:13:01,000
然后它去汁液。

310
00:13:01,000 --> 00:13:02,160
它仍然在我们这里。

311
00:13:02,160 --> 00:13:02,680
是的。

312
00:13:02,680 --> 00:13:05,480
所以给出一些唯一的号码来稍后识别，

313
00:13:05,480 --> 00:13:07,480
甚至用于对账。

314
00:13:07,480 --> 00:13:10,080
如果出了问题，它没有工作。

315
00:13:10,080 --> 00:13:10,760
是的。

316
00:13:10,760 --> 00:13:13,000
它没有发布到汁液。

317
00:13:13,000 --> 00:13:14,400
我认为我们仍然需要一些唯一的。

318
00:13:14,400 --> 00:13:16,200
我们可以创建一个基金。

319
00:13:16,200 --> 00:13:18,600
我们不能创建基金，你的意思是？

320
00:13:18,600 --> 00:13:19,240
我们可以。

321
00:13:19,240 --> 00:13:20,880
我们可以从一开始就做。

322
00:13:20,880 --> 00:13:24,200
这就是vars博士说的。

323
00:13:24,200 --> 00:13:25,680
因为我必须触发某些东西，

324
00:13:25,680 --> 00:13:27,400
我把它放在触发逻辑中，史丹利。

325
00:13:27,400 --> 00:13:30,640
否则，你可以在工作区时分配它。

326
00:13:30,640 --> 00:13:34,240
有一件事是在流程模板上。

327
00:13:34,240 --> 00:13:37,040
当工作区命名时，我们使用了那个ddr8。

328
00:13:37,040 --> 00:13:37,560
是的。

329
00:13:37,560 --> 00:13:40,240
这是有一些序列号的某种方式，

330
00:13:40,240 --> 00:13:41,440
要附加的东西。

331
00:13:41,440 --> 00:13:42,000
是的。

332
00:13:42,000 --> 00:13:43,280
然后它会生成。

333
00:13:44,240 --> 00:13:45,880
这只是为了参考，史丹利。

334
00:13:45,880 --> 00:13:47,240
否则，他们会感到困惑。

335
00:13:47,240 --> 00:13:49,080
这么多工作流程，这么多事情。

336
00:13:49,080 --> 00:13:50,680
所以他们会感到困惑。

337
00:13:50,680 --> 00:13:52,720
是的。

338
00:13:52,720 --> 00:13:54,320
这是一些日期和时间戳，某些东西。

339
00:13:54,320 --> 00:13:56,600
也许我们可以使用逻辑来使其唯一。

340
00:13:56,600 --> 00:13:58,520
哦，是的，那很好。

341
00:13:58,520 --> 00:14:00,040
运行号码应该没问题。

342
00:14:00,040 --> 00:14:03,000
号码，唯一的问题是，他们需要一个用于pr，一个用于po，

343
00:14:03,000 --> 00:14:04,480
一个用于prn。

344
00:14:04,480 --> 00:14:07,560
这不是可以基于过程制作的，

345
00:14:07,560 --> 00:14:09,320
基于模板。

346
00:14:09,320 --> 00:14:11,800
这就是我在想的。

347
00:14:11,800 --> 00:14:13,720
我们需要一种方法来命名它。

348
00:14:13,720 --> 00:14:15,400
现在，我会审查stanley。

349
00:14:15,400 --> 00:14:19,160
我的意思是，我会批准它作为采购申请，对吧？

350
00:14:19,160 --> 00:14:21,560
所以你会看到它通过。

351
00:14:25,000 --> 00:14:27,240
所以有人提出了它，有人批准了它。

352
00:14:27,240 --> 00:14:27,720
是的。

353
00:14:27,720 --> 00:14:29,240
那现在会去汁液。

354
00:14:29,240 --> 00:14:30,200
SAP，是的。

355
00:14:30,200 --> 00:14:31,200
是的。

356
00:14:31,200 --> 00:14:32,160
它如何去汁液？

357
00:14:36,440 --> 00:14:38,280
好吧，sap，对吧？，对吧？

358
00:14:38,280 --> 00:14:41,560
任何人安装b1，当他们更新ai服务

359
00:14:41,560 --> 00:14:42,400
你在谈论的，对吧？

360
00:14:42,400 --> 00:14:43,080
是的，是的，是的。

361
00:14:43,080 --> 00:14:45,240
他们都有非常相似的界面，对吧？

362
00:14:45,240 --> 00:14:47,320
我们可以构建一个连接器来工作。

363
00:14:47,320 --> 00:14:48,000
是的，是的，是的。

364
00:14:48,000 --> 00:14:49,360
我们也会讨论那个。

365
00:14:49,360 --> 00:14:50,520
它应该工作。

366
00:14:50,520 --> 00:14:51,880
我会给你展示源代码。

367
00:14:51,880 --> 00:14:53,960
你有那个源代码。

368
00:14:53,960 --> 00:14:56,400
所以基本上，你可以暴露所有对象

369
00:14:56,400 --> 00:14:59,240
我们想要作为获取api。

370
00:14:59,240 --> 00:15:00,680
他准备这样做。

371
00:15:00,680 --> 00:15:01,920
实际上，他在说。

372
00:15:01,920 --> 00:15:04,640
这现在只能在内部安装，是吗？

373
00:15:04,640 --> 00:15:05,960
没有云版本。

374
00:15:05,960 --> 00:15:06,640
没有云。

375
00:15:06,640 --> 00:15:09,360
SAP B1根本不是云。

376
00:15:09,360 --> 00:15:10,200
是的。

377
00:15:10,200 --> 00:15:20,680
所以在那个表中，我们刚刚看到了视图列表。

378
00:15:20,680 --> 00:15:25,160
那是需要从汁液来的数据。

379
00:15:25,160 --> 00:15:26,560
多个下拉菜单，对吧？

380
00:15:26,560 --> 00:15:27,880
是的，多个下拉菜单。

381
00:15:27,880 --> 00:15:31,440
那些下拉菜单可能彼此有关系。

382
00:15:31,440 --> 00:15:33,080
依赖性非常，非常常见。

383
00:15:33,080 --> 00:15:34,920
是的，非常常见。

384
00:15:34,920 --> 00:15:39,920
那个下拉菜单，即使下拉菜单显示

385
00:15:39,920 --> 00:15:43,200
可能是一个表格。

386
00:15:43,200 --> 00:15:43,960
可能。

387
00:15:43,960 --> 00:15:44,440
对。

388
00:15:44,440 --> 00:15:45,640
多于一个值，是的。

389
00:15:45,640 --> 00:15:46,600
多于一个值。

390
00:15:46,600 --> 00:15:47,440
像数据表。

391
00:15:47,440 --> 00:15:53,440
是的，数据表，然后你选择使用哪一个。

392
00:15:53,440 --> 00:15:58,280
数据表，如果我们想要，我们也可以显示更多作为文本信息。

393
00:15:58,280 --> 00:16:00,680
是的，然后你只选择一个。

394
00:16:00,680 --> 00:16:01,680
无论如何，只有一个。

395
00:16:01,680 --> 00:16:02,840
是的，然后你选择一个。

396
00:16:02,840 --> 00:16:03,480
是的。

397
00:16:03,480 --> 00:16:05,680
sap获得多于一个数据

398
00:16:05,680 --> 00:16:07,200
对于同一个角色。

399
00:16:07,200 --> 00:16:07,800
是的。

400
00:16:07,800 --> 00:16:09,760
像帕拉夫·辛格（Palav Singh），是的。

401
00:16:10,600 --> 00:16:13,080
然后你可以获取它并显示它。

402
00:16:13,080 --> 00:16:13,600
是的。

403
00:16:13,600 --> 00:16:17,520
你可以获得1000个左右。

404
00:16:17,520 --> 00:16:18,040
对。

405
00:16:18,040 --> 00:16:21,360
下拉菜单中的一个空间。

406
00:16:21,360 --> 00:16:22,680
那可能仍然没问题。

407
00:16:22,680 --> 00:16:26,360
所以这里是采购请求，sap。

408
00:16:26,360 --> 00:16:30,080
如你所知，我们选择了30和40，两个项目。

409
00:16:30,080 --> 00:16:32,840
建议的供应商也来了。

410
00:16:32,840 --> 00:16:35,880
所以有时他们可能也不给供应商。

411
00:16:35,880 --> 00:16:38,320
这取决于他们，但这个组织

412
00:16:38,320 --> 00:16:40,280
也想要建议的供应商。

413
00:16:40,280 --> 00:16:42,160
因为这完全取决于他们如何设置。

414
00:16:42,160 --> 00:16:43,280
正确，正确，正确。

415
00:16:43,280 --> 00:16:44,640
是的，关于那个一点。

416
00:16:44,640 --> 00:16:47,680
但是是的，供应商，他们有一些地方

417
00:16:47,680 --> 00:16:49,360
向你展示供应商。

418
00:16:49,360 --> 00:16:49,960
是的，是的，是的。

419
00:16:49,960 --> 00:16:52,520
这就是我们在第一个链接中做的。

420
00:16:52,520 --> 00:16:55,200
是的，在下拉菜单中，在下拉菜单中，是的。

421
00:16:55,200 --> 00:16:56,440
但那是，是的。

422
00:16:56,440 --> 00:16:57,520
对不起，palav。

423
00:16:57,520 --> 00:16:58,760
也许你可以完成。

424
00:16:58,760 --> 00:17:02,720
是的，所以uom，一切都过来了，对吧？，对吧？

425
00:17:02,720 --> 00:17:03,320
现在。

426
00:17:03,320 --> 00:17:05,600
您没有采取任何措施将供应商拆分出来，对吗？

427
00:17:05,600 --> 00:17:06,360
不，不，不。

428
00:17:06,360 --> 00:17:08,200
您只需发送项目编号。

429
00:17:08,320 --> 00:17:12,160
哦，我们确实将V006供应商代码分开了。

430
00:17:12,160 --> 00:17:13,480
他们在途中做了一些工作。

431
00:17:13,480 --> 00:17:14,320
是的，是的。

432
00:17:14,320 --> 00:17:17,920
在返回的路上，我们必须将其分开。

433
00:17:17,920 --> 00:17:21,000
好的，所以现在从这一次，可以说

434
00:17:21,000 --> 00:17:24,400
我想将其放入采购订单。

435
00:17:24,400 --> 00:17:26,600
作为采购人，我将直接

436
00:17:26,600 --> 00:17:29,400
创建这样的采购订单。

437
00:17:29,400 --> 00:17:34,480
问题是，为什么我说这是建议的供应商。

438
00:17:34,480 --> 00:17:35,760
现在他们将把真实的人放在。

439
00:17:35,760 --> 00:17:37,600
现在，他们将把真正的供应商站立。

440
00:17:37,600 --> 00:17:40,200
因为他们可能会要求其他供应商

441
00:17:40,200 --> 00:17:42,080
对于不同的订单项，对吗？

442
00:17:42,080 --> 00:17:44,880
但是采购部门的研究员将决定。

443
00:17:44,880 --> 00:17:46,720
现在他还可以删除此项目，

444
00:17:46,720 --> 00:17:48,960
说我会创建另一个采购订单。

445
00:17:48,960 --> 00:17:51,160
我不想混合采购订单。

446
00:17:51,160 --> 00:17:53,520
因此，这都是一项采购决定。

447
00:17:53,520 --> 00:17:55,480
但是我们不需要为所有这些困扰。

448
00:17:55,480 --> 00:17:58,640
基本上，他们会在SAP中完成所有这些，对吗？

449
00:17:58,640 --> 00:18:00,480
所以他们做了这一切。

450
00:18:00,480 --> 00:18:02,400
仓库也是重要的事情。

451
00:18:02,400 --> 00:18:06,120
哦，这是我想告诉的另一件事。

452
00:18:06,520 --> 00:18:08,520
在用户配置文件中，我们已配置

453
00:18:08,520 --> 00:18:10,520
该人所属的仓库

454
00:18:10,520 --> 00:18:13,000
到，部门代码。

455
00:18:13,000 --> 00:18:14,920
重要的原因是

456
00:18:14,920 --> 00:18:18,760
限制您在SAP中的可见性。

457
00:18:18,760 --> 00:18:21,120
采购订单，什么采购订单

458
00:18:21,120 --> 00:18:23,720
您可以看到收到的商品。

459
00:18:23,720 --> 00:18:27,080
对于其他事情而言，这并不重要。

460
00:18:27,080 --> 00:18:29,600
但仅适用于收到的商品。

461
00:18:29,600 --> 00:18:32,320
在一个采购订单中，假设有五个项目。

462
00:18:32,320 --> 00:18:35,160
仓库中的每个项目都可以标记。

463
00:18:35,160 --> 00:18:37,200
因此，如果三个在一个仓库中，

464
00:18:37,200 --> 00:18:38,960
其他两个在另一个，那个仓库，

465
00:18:38,960 --> 00:18:40,800
一个人不应该看到另外两个。

466
00:18:40,800 --> 00:18:41,800
他只能看到这三个。

467
00:18:41,800 --> 00:18:42,640
正确，正确。

468
00:18:42,640 --> 00:18:45,080
但是您可以在获取中做，获取API。

469
00:18:45,080 --> 00:18:45,920
在Get API中，您可以做到这一点。

470
00:18:45,920 --> 00:18:48,040
在数据捕获中，我们不应该担心。

471
00:18:48,040 --> 00:18:51,480
谁在预订的人应该非常清楚GRN

472
00:18:51,480 --> 00:18:52,800
他属于仓库。

473
00:18:52,800 --> 00:18:53,640
正确，正确，正确。

474
00:18:53,640 --> 00:18:54,840
因此他的能见度可以得到控制。

475
00:18:54,840 --> 00:18:55,680
是的，是的。

476
00:18:55,680 --> 00:18:56,520
因此用户...

477
00:18:56,520 --> 00:18:58,680
这是我们的逻辑，那是SAP逻辑

478
00:18:58,680 --> 00:19:01,040
当我们过滤掉时，对吗？

479
00:19:01,040 --> 00:19:04,400
您可以通过复杂的查询来做到这一点

480
00:19:04,400 --> 00:19:06,520
这意味着您必须看...

481
00:19:06,520 --> 00:19:08,360
另外，我们会把它提取出来，对吗？

482
00:19:08,360 --> 00:19:09,480
我们正在建造什么连接器，

483
00:19:09,480 --> 00:19:10,720
所有这些逻辑都应该进入内部。

484
00:19:10,720 --> 00:19:11,560
你可以。

485
00:19:11,560 --> 00:19:12,960
这是您必须做出的决定。

486
00:19:12,960 --> 00:19:14,920
我的意思是，您想在哪里放置该逻辑。

487
00:19:14,920 --> 00:19:16,800
因为那与我们的形式无关，对吗？

488
00:19:16,800 --> 00:19:18,280
是的，我的意思是，是的。

489
00:19:18,280 --> 00:19:21,360
这只是我允许选择的项目。

490
00:19:21,360 --> 00:19:24,120
那个逻辑，您可以决定它的去向。

491
00:19:24,120 --> 00:19:26,000
现在，我们已经以形式说明

492
00:19:26,000 --> 00:19:27,800
在用户显示ID中。

493
00:19:27,800 --> 00:19:30,160
是的，用户ID并这样使用。

494
00:19:30,160 --> 00:19:31,000
是的。

495
00:19:31,960 --> 00:19:32,800
正确的。

496
00:19:32,800 --> 00:19:34,640
连接器应该有。

497
00:19:34,640 --> 00:19:35,480
是的。

498
00:19:35,480 --> 00:19:37,560
苹果应该具有逻辑。

499
00:19:37,560 --> 00:19:38,880
我建议的，斯坦利，

500
00:19:38,880 --> 00:19:41,360
是每个人都有默认实现

501
00:19:41,360 --> 00:19:43,840
API，对吗？

502
00:19:43,840 --> 00:19:46,560
有一定的查询，SQL查询，对吗？

503
00:19:46,560 --> 00:19:49,880
如果我愿意，我们的团队可以更改SQL查询

504
00:19:49,880 --> 00:19:51,920
对于特定组织，如果需要。

505
00:19:51,920 --> 00:19:52,760
这样的东西。

506
00:19:52,760 --> 00:19:55,360
最有可能的是，我们将通过一个框架构建它，

507
00:19:55,360 --> 00:19:57,000
集成框架，一种应用程序。

508
00:19:57,000 --> 00:19:57,840
好吧，好吧。

509
00:19:57,840 --> 00:19:58,680
因此，应该有一种方法

510
00:19:59,600 --> 00:20:01,320
自定义逻辑。

511
00:20:01,320 --> 00:20:02,160
好吧，好吧。

512
00:20:02,160 --> 00:20:03,000
好的。

513
00:20:03,000 --> 00:20:03,840
很好，很好。

514
00:20:03,840 --> 00:20:04,680
我的意思是，我只是在说。

515
00:20:04,680 --> 00:20:05,520
是的，你是对的。

516
00:20:05,520 --> 00:20:06,680
是的，是的。

517
00:20:06,680 --> 00:20:09,440
也许他们可以选择其他地方，

518
00:20:09,440 --> 00:20:11,560
然后，这将显示这些项目。

519
00:20:11,560 --> 00:20:12,880
他们想收到的地方。

520
00:20:12,880 --> 00:20:14,800
但是，无论是否可以接受，

521
00:20:14,800 --> 00:20:16,480
我们需要检查...

522
00:20:16,480 --> 00:20:17,960
我宁愿所有这些

523
00:20:17,960 --> 00:20:19,800
我们现在可以将其保留更多的编码，对吗？

524
00:20:19,800 --> 00:20:20,640
是的，是的，是的。

525
00:20:20,640 --> 00:20:21,640
这可能是很多场景。

526
00:20:21,640 --> 00:20:22,920
是啊是啊。

527
00:20:22,920 --> 00:20:23,760
所以，好的。

528
00:20:23,760 --> 00:20:28,440
现在，我选择了供应商。

529
00:20:28,440 --> 00:20:30,800
我必须选择PO上的单价。

530
00:20:30,800 --> 00:20:34,400
这将是我与供应商同意的事情，对吗？

531
00:20:34,400 --> 00:20:36,680
通常，在更复杂的SAP系统中，

532
00:20:36,680 --> 00:20:38,680
这都是从合同中获取的

533
00:20:38,680 --> 00:20:40,440
否则会违约，

534
00:20:40,440 --> 00:20:44,280
否则将从另一个系统发生一些价格确定。

535
00:20:44,280 --> 00:20:46,120
但是PO的创建会在这里发生，对吗？

536
00:20:46,120 --> 00:20:46,960
正确，正确。

537
00:20:46,960 --> 00:20:48,120
那，他们不在外面做。

538
00:20:48,120 --> 00:20:48,960
不，不，不。

539
00:20:48,960 --> 00:20:51,200
他们将不得不在SAP中进行。

540
00:20:51,200 --> 00:20:53,560
因此，我们不必担心这种逻辑，

541
00:20:53,560 --> 00:20:57,040
因为即使他们放置了复杂的逻辑，

542
00:20:57,040 --> 00:21:00,280
我们只需要处理采购订单即可。

543
00:21:00,280 --> 00:21:02,360
所以，我只说，在这里添加一个新的。

544
00:21:02,360 --> 00:21:05,200
因此，如您所知，Sally有两个项目。

545
00:21:05,200 --> 00:21:06,840
他现在又添加了吗？

546
00:21:06,840 --> 00:21:07,680
不，不。

547
00:21:07,680 --> 00:21:08,520
只有两个项目。

548
00:21:08,520 --> 00:21:09,360
就是这样。

549
00:21:09,360 --> 00:21:11,080
我也可以添加更多项目

550
00:21:11,080 --> 00:21:13,160
从另一个购买请求中。

551
00:21:13,160 --> 00:21:16,400
因此，这一切都是可能的。

552
00:21:16,400 --> 00:21:18,040
现在，我们已经做到了，对吗？

553
00:21:18,040 --> 00:21:18,880
那就完成了。

554
00:21:19,720 --> 00:21:22,400
现在，如果您查看用户定义的字段，

555
00:21:22,400 --> 00:21:25,280
我们所做的是保持同步，

556
00:21:25,280 --> 00:21:29,360
您会看到这一点，要说这是否发布在莫克索，

557
00:21:29,360 --> 00:21:31,080
我们说这不是默认情况下。

558
00:21:31,080 --> 00:21:34,160
因此，一旦他获得回报，成功的回报，

559
00:21:35,200 --> 00:21:37,480
他在这里做到了。

560
00:21:37,480 --> 00:21:41,400
因此，这是我们为此需要运行的EXE。

561
00:21:41,400 --> 00:21:42,240
现在将同步。

562
00:21:42,240 --> 00:21:43,080
现在将同步。

563
00:21:43,080 --> 00:21:44,800
它将寻找所有非寄存的莫克斯。

564
00:21:44,800 --> 00:21:45,640
确切地。

565
00:21:45,640 --> 00:21:50,640
它会寻找所有非寄存的POS，然后这样做，好吗？

566
00:21:51,640 --> 00:21:54,000
现在，我将刷新此唱片。

567
00:21:54,000 --> 00:21:55,800
您会在这里看到它是“是”。

568
00:21:55,800 --> 00:21:57,520
它发布到莫克索。

569
00:21:57,520 --> 00:21:59,680
如果您注意到PO号，它说59。

570
00:21:59,680 --> 00:22:02,320
这是我们将搜索的参考号。

571
00:22:02,320 --> 00:22:03,560
SAP编号。

572
00:22:03,560 --> 00:22:05,200
Bala是什么ID？

573
00:22:05,200 --> 00:22:06,040
哪一个？

574
00:22:06,040 --> 00:22:06,880
ID Moxo。

575
00:22:08,000 --> 00:22:10,480
id Moxo不过是在他的原木中

576
00:22:10,480 --> 00:22:11,640
他正在打印所有这些。

577
00:22:11,640 --> 00:22:12,480
一些ID。

578
00:22:12,480 --> 00:22:15,360
是的，这是您可以关联的ID

579
00:22:15,360 --> 00:22:17,040
在SAP侧的日志中。

580
00:22:17,040 --> 00:22:17,880
只是为了查看集成。

581
00:22:17,880 --> 00:22:21,200
是的，在SAP一侧，您可以看到日志等。

582
00:22:21,200 --> 00:22:23,280
所以，这是他们生成的一些ID

583
00:22:23,280 --> 00:22:24,920
我们只是在存储它。

584
00:22:24,920 --> 00:22:26,240
我们可能不会显示。

585
00:22:26,240 --> 00:22:27,080
我的意思是，这取决于我们。

586
00:22:27,080 --> 00:22:28,560
不，这很适合安装。

587
00:22:28,560 --> 00:22:30,040
是的，是的。

588
00:22:30,040 --> 00:22:31,120
因此，这部分完成了。

589
00:22:31,120 --> 00:22:36,120
现在，PO来了供应商，对吗？

590
00:22:36,200 --> 00:22:39,840
它也出现在供应商身上。

591
00:22:43,720 --> 00:22:45,600
所以，我们在这里所做的，斯坦利，

592
00:22:45,600 --> 00:22:48,800
默认该中间件，它的作用是

593
00:22:48,800 --> 00:22:50,920
它创建了供应商通信活页夹

594
00:22:50,920 --> 00:22:53,240
如果不存在，好吗？

595
00:22:54,080 --> 00:22:56,040
它贴上粘合剂标签，好吗？

596
00:22:56,040 --> 00:22:58,160
所以，活页夹标签说，好的，

597
00:22:58,160 --> 00:23:01,640
是否已经有一个通信文件夹？

598
00:23:01,640 --> 00:23:04,760
如果在那里，它将仅将PO放入该文件夹中。

599
00:23:04,760 --> 00:23:06,520
是的，所以，将新的流动放在那里。

600
00:23:06,520 --> 00:23:07,360
将其放入收件箱中。

601
00:23:07,360 --> 00:23:08,320
放一个新的流程。

602
00:23:08,320 --> 00:23:09,520
不，我们只是放置了一个新的流程。

603
00:23:09,520 --> 00:23:10,520
不需要，斯坦利。

604
00:23:10,520 --> 00:23:12,400
只有一个动作。

605
00:23:12,400 --> 00:23:14,520
您也可以将其放在收件箱上。

606
00:23:14,520 --> 00:23:17,240
所以，我可以说的是新流程，

607
00:23:17,240 --> 00:23:19,040
别无其他，但这只是为了。

608
00:23:19,040 --> 00:23:19,880
当然，当然。

609
00:23:19,880 --> 00:23:22,240
然后，因为无论如何

610
00:23:22,880 --> 00:23:24,840
只有说一个动作或多个。

611
00:23:24,840 --> 00:23:25,880
是的，那也很好。

612
00:23:25,880 --> 00:23:26,720
是的，是的。

613
00:23:26,720 --> 00:23:28,040
是的，是的。

614
00:23:28,040 --> 00:23:28,880
这是一步。

615
00:23:28,880 --> 00:23:29,720
单一动作。

616
00:23:29,720 --> 00:23:30,560
一步流。

617
00:23:30,560 --> 00:23:32,440
一步流，是的。

618
00:23:32,440 --> 00:23:36,160
因此，现在他必须审查并承认PO。

619
00:23:36,160 --> 00:23:38,800
因此，如果他知道，我们将此限制

620
00:23:38,800 --> 00:23:39,920
说他们必须审查。

621
00:23:39,920 --> 00:23:41,560
所以，他们很高兴说，哦，是的，

622
00:23:41,560 --> 00:23:43,200
现在我们正在确保这一点。

623
00:23:43,200 --> 00:23:44,040
他正在读书。

624
00:23:44,040 --> 00:23:44,860
他正在阅读。

625
00:23:44,860 --> 00:23:47,400
因此，我们在这里仅创建了一个HTML文档

626
00:23:47,400 --> 00:23:50,200
根据我们从SAP收到的PO信息。

627
00:23:51,160 --> 00:23:53,960
这意味着它平等。

628
00:23:53,960 --> 00:23:55,600
不，不，不。

629
00:23:55,600 --> 00:23:58,120
我们的中间件创造了这个。

630
00:23:58,120 --> 00:24:01,040
从SAP数据中，我们生成了a。

631
00:24:01,040 --> 00:24:03,320
斯坦利也可以是一个确认对象。

632
00:24:03,320 --> 00:24:06,160
我只是让它好一些。

633
00:24:06,160 --> 00:24:07,760
它可以是PDF。

634
00:24:07,760 --> 00:24:08,600
它可以是PDF。

635
00:24:08,600 --> 00:24:10,400
不错的PDF。

636
00:24:10,400 --> 00:24:14,960
但是随后SAP继续生成此PDF。

637
00:24:14,960 --> 00:24:17,240
不，不，不是B1。

638
00:24:17,240 --> 00:24:19,080
有SAP的版本

639
00:24:19,120 --> 00:24:21,200
也生成文档。

640
00:24:21,200 --> 00:24:24,800
但为此，我的意思是，您也可以构建此逻辑

641
00:24:24,800 --> 00:24:26,160
如果需要，请在连接器中。

642
00:24:26,160 --> 00:24:27,600
您可以从那里发送PDF。

643
00:24:27,600 --> 00:24:30,240
当然，我们需要一个非常强大的连接器来支持它。

644
00:24:30,240 --> 00:24:32,240
所以，我的意思是，这是我们的决定。

645
00:24:32,240 --> 00:24:33,800
是的，我们将进一步抽象。

646
00:24:33,800 --> 00:24:35,320
我们只进行更改。

647
00:24:35,320 --> 00:24:37,080
因为，斯坦利，您有多个项目。

648
00:24:37,080 --> 00:24:38,760
这也可能很长。

649
00:24:38,760 --> 00:24:40,280
它也可能有10个项目。

650
00:24:40,280 --> 00:24:42,760
因此，您实际上无法真正将其放在卡中

651
00:24:42,760 --> 00:24:43,680
确认卡。

652
00:24:43,680 --> 00:24:47,120
您可能需要将其显示为适当的桌子等。

653
00:24:47,160 --> 00:24:52,800
因此，现在我承认这一点并确认。

654
00:24:52,800 --> 00:24:56,040
因此，这是正确的。

655
00:24:56,040 --> 00:24:58,200
现在，当我运送货物时，

656
00:24:58,200 --> 00:25:03,560
我可以来提出ASN高级发货通知。

657
00:25:03,560 --> 00:25:07,120
那么，他们如何承认呢？

658
00:25:07,120 --> 00:25:09,840
致谢，而不是B1，斯坦利。

659
00:25:09,840 --> 00:25:12,160
没有地方可以保留它。

660
00:25:12,160 --> 00:25:14,200
他们可能会做一件事，斯坦利。

661
00:25:14,200 --> 00:25:17,240
他们可能会创建一个用户定义的字段

662
00:25:17,240 --> 00:25:19,480
称为从供应商那里收到的确认。

663
00:25:19,480 --> 00:25:21,040
他们可以做到。

664
00:25:21,040 --> 00:25:22,720
然后将其存放在那里。

665
00:25:22,720 --> 00:25:23,600
我们可以将其发送回。

666
00:25:23,600 --> 00:25:24,120
是的。

667
00:25:24,120 --> 00:25:26,000
人们，他们知道，他们会改变一点

668
00:25:26,000 --> 00:25:27,480
根据他们的要求。

669
00:25:27,480 --> 00:25:29,200
确认过程可能不会

670
00:25:29,200 --> 00:25:30,480
对于某些业务也必须强制性。

671
00:25:30,480 --> 00:25:30,960
正确的。

672
00:25:30,960 --> 00:25:34,160
他们也可能不希望确认其中一些。

673
00:25:34,160 --> 00:25:36,880
我的意思是，就像，他们还可以，没有任何认可。

674
00:25:36,880 --> 00:25:39,680
但这可以提高我们的价值。

675
00:25:39,680 --> 00:25:41,480
这可能是电子邮件的认可

676
00:25:41,480 --> 00:25:42,440
另外，目前，为此。

677
00:25:42,440 --> 00:25:43,480
正确，正确。

678
00:25:43,480 --> 00:25:46,160
现在，您知道，我已经扁平了采购订单。

679
00:25:46,160 --> 00:25:50,240
我没有单独显示采购订单和项目

680
00:25:50,240 --> 00:25:50,800
分别地。

681
00:25:50,800 --> 00:25:54,880
我已经将所有细节变成一条线。

682
00:25:54,880 --> 00:25:56,200
是的，这里很多。

683
00:25:56,200 --> 00:25:56,840
是的。

684
00:25:56,840 --> 00:25:58,640
所以现在，您迅速打开它，对，巴拉？

685
00:25:58,640 --> 00:25:59,160
是啊是啊。

686
00:25:59,160 --> 00:26:02,040
这是供应商门户交互，对吗？

687
00:26:02,040 --> 00:26:02,600
正确的。

688
00:26:02,600 --> 00:26:03,920
所以现在，供应商来了。

689
00:26:03,920 --> 00:26:05,360
他正在确认。

690
00:26:05,360 --> 00:26:06,880
他有很多订单。

691
00:26:06,880 --> 00:26:08,320
他说的是，我现在要运送这个。

692
00:26:08,320 --> 00:26:08,800
正确的。

693
00:26:08,800 --> 00:26:11,440
所以他现在正在提出发货通知

694
00:26:11,440 --> 00:26:12,240
向业务。

695
00:26:12,240 --> 00:26:14,760
正确的。

696
00:26:14,760 --> 00:26:18,480
现在，为了简化它，我们使UI略有不同。

697
00:26:18,480 --> 00:26:19,960
好的，这里有一些错误。

698
00:26:19,960 --> 00:26:21,160
我可以卖掉一个项目。

699
00:26:21,160 --> 00:26:22,640
这就是我们这样做的年龄。

700
00:26:22,640 --> 00:26:25,680
是的，这就是我们这样做的年龄。

701
00:26:25,680 --> 00:26:30,320
这是，我使它尽可能灵活

702
00:26:30,320 --> 00:26:31,960
我的意思是，我稍后再谈论。

703
00:26:31,960 --> 00:26:33,720
希望我们能摆脱所有这些。

704
00:26:33,720 --> 00:26:36,240
您可以摆脱汁液。

705
00:26:36,240 --> 00:26:37,760
在桌子架上，在邮箱中。

706
00:26:37,760 --> 00:26:41,160
目前，巴拉还在本地存储所有东西。

707
00:26:41,160 --> 00:26:43,360
因此，此数据现在在SAP中？

708
00:26:43,360 --> 00:26:45,000
这些数据已经在SAP中了，对吗？

709
00:26:45,000 --> 00:26:48,080
这来自采购订单数据SAP。

710
00:26:48,080 --> 00:26:52,920
现在，如果我创建一个ASN，它将仅在这里。

711
00:26:52,920 --> 00:26:54,680
它将交给买家。

712
00:26:54,680 --> 00:26:57,160
但是您也可以放置用户定义的字段

713
00:26:57,160 --> 00:26:59,200
在B1中，可以说，

714
00:26:59,200 --> 00:27:00,320
它得到了进步。

715
00:27:00,320 --> 00:27:03,120
因此，在您的应用程序中，您只存储新数据？

716
00:27:03,120 --> 00:27:03,920
是啊是啊。

717
00:27:03,920 --> 00:27:04,680
在释放后。

718
00:27:04,680 --> 00:27:06,920
实际上，它可以被获取。

719
00:27:06,920 --> 00:27:08,880
如果您有连接器，则可以直播。

720
00:27:08,880 --> 00:27:10,480
是的，您可以活这个，史丹利。

721
00:27:10,480 --> 00:27:12,200
您不需要这个中层，对吗？

722
00:27:12,200 --> 00:27:16,880
因为如果我有一个连接器，我的意思是，那是活的，对吗？

723
00:27:16,880 --> 00:27:19,200
活汁。

724
00:27:19,200 --> 00:27:19,700
是的。

725
00:27:19,700 --> 00:27:23,000
因此，这意味着SAP具有SM数据。

726
00:27:23,000 --> 00:27:23,680
不是Asn。

727
00:27:23,680 --> 00:27:24,640
他们有PO数据。

728
00:27:24,640 --> 00:27:26,200
现在，我正在选择PO。

729
00:27:26,200 --> 00:27:28,680
这是该供应商的PO数据？

730
00:27:28,680 --> 00:27:29,960
对于那个供应商。

731
00:27:29,960 --> 00:27:32,080
因为这是我运送的物品。

732
00:27:32,080 --> 00:27:33,080
实际上有一个错误。

733
00:27:33,080 --> 00:27:34,600
它选择了所有数据。

734
00:27:34,600 --> 00:27:36,560
当我进行搜索时，它正在选择任何内容。

735
00:27:36,560 --> 00:27:41,160
因此，您现在正在谈论您的供应商评论。

736
00:27:41,160 --> 00:27:42,640
是的。

737
00:27:42,640 --> 00:27:47,360
那意味着将来，我们的供应商

738
00:27:47,360 --> 00:27:53,120
可以勾选一些ASN过程。

739
00:27:53,120 --> 00:27:53,880
是的。

740
00:27:53,880 --> 00:27:56,520
通过启动，这很容易。

741
00:27:56,520 --> 00:27:59,640
当他们勾选这一点时，他们需要捡起。

742
00:27:59,640 --> 00:28:05,840
然后，您去SAP选择了哪个列表 - 

743
00:28:05,840 --> 00:28:06,960
为我购买订单。

744
00:28:06,960 --> 00:28:08,760
PO，他们参与其中。

745
00:28:08,760 --> 00:28:09,640
是的，是的。

746
00:28:09,640 --> 00:28:12,320
今天，这完全是黑匣子。

747
00:28:12,320 --> 00:28:14,560
因为供应商无法访问SAP。

748
00:28:14,560 --> 00:28:17,080
因此，我们一直在带上桥。

749
00:28:17,080 --> 00:28:19,520
因此，企业可能会发现这非常有价值。

750
00:28:19,520 --> 00:28:20,200
正确，正确。

751
00:28:20,200 --> 00:28:22,960
但是，供应商真的会受到纪律处分吗？

752
00:28:22,960 --> 00:28:24,400
不，这是可选的。

753
00:28:24,400 --> 00:28:26,840
因此，在许多情况下，他们说，不，我们

754
00:28:26,840 --> 00:28:28,080
也不需要这个。

755
00:28:28,080 --> 00:28:30,400
他们将直接去收到的商品。

756
00:28:30,400 --> 00:28:32,200
看，我是说我们展示这一切。

757
00:28:32,200 --> 00:28:34,440
从商业方面，操作可能受到限制。

758
00:28:34,440 --> 00:28:35,760
从商业方面，它是可选的。

759
00:28:35,760 --> 00:28:36,800
如果他们愿意，他们可以。

760
00:28:36,800 --> 00:28:37,480
正确，正确。

761
00:28:37,480 --> 00:28:39,080
但是我们只是显示所有选项。

762
00:28:39,080 --> 00:28:40,080
是的，选项在那里。

763
00:28:40,080 --> 00:28:42,600
因此，供应商可能

764
00:28:42,600 --> 00:28:46,200
但是，从某种意义上说，我们必须非常小心

765
00:28:46,200 --> 00:28:49,080
那个供应商只能看到他的SEO等。

766
00:28:49,080 --> 00:28:50,960
但是您可以在查询中控制。

767
00:28:50,960 --> 00:28:53,720
在查询中，您可以在SAP查询中控制，对吗？

768
00:28:53,720 --> 00:28:55,120
是的。

769
00:28:55,120 --> 00:28:56,320
连接器。

770
00:28:56,320 --> 00:28:57,400
自动化和 - 

771
00:28:57,400 --> 00:28:58,440
正确，正确，正确。

772
00:28:58,440 --> 00:28:58,920
知道了。

773
00:28:58,920 --> 00:28:59,600
是的。

774
00:28:59,600 --> 00:29:01,720
那就是您可能需要增强用户的地方

775
00:29:01,760 --> 00:29:04,480
简介一点点，说，这是供应商。

776
00:29:04,480 --> 00:29:05,880
我的意思是，一些存储 - 

777
00:29:05,880 --> 00:29:09,200
我们通过过程控制它们。

778
00:29:09,200 --> 00:29:11,520
这是ASL过程。

779
00:29:11,520 --> 00:29:15,160
然后，在自动化的内部，您可以在那里。

780
00:29:15,160 --> 00:29:17,480
谁是供应商？

781
00:29:17,480 --> 00:29:19,920
自动化第二批。

782
00:29:19,920 --> 00:29:23,720
自动化，您需要选择您是谁，

783
00:29:23,720 --> 00:29:25,240
这是什么。

784
00:29:25,240 --> 00:29:28,480
一种方法是导入这两个变量，对吗？

785
00:29:28,480 --> 00:29:31,600
如果不输入供应商ID，就无法启动ASN。

786
00:29:31,600 --> 00:29:34,160
我们可以自动填充供应商ID。

787
00:29:34,160 --> 00:29:35,560
但是在最坏的情况下，手动，你

788
00:29:35,560 --> 00:29:37,560
必须输入我的供应商ID，然后将

789
00:29:37,560 --> 00:29:38,800
做某种

790
00:29:38,800 --> 00:29:41,920
所以我做的是再次使用了用户ID字段

791
00:29:41,920 --> 00:29:46,640
对于客户来说，我的意思是供应商实际上控制这一点。

792
00:29:46,640 --> 00:29:48,440
我们必须考虑一下。

793
00:29:48,440 --> 00:29:49,600
是的，我只是在说什么

794
00:29:49,600 --> 00:29:51,120
因为你也不能滥用，对吗？

795
00:29:51,120 --> 00:29:51,800
在这里放一些ID。

796
00:29:51,800 --> 00:29:58,920
我们可以执行一个过程，因此在供应商中的一个文件。

797
00:29:58,920 --> 00:30:00,520
不，那将是太多的时间。

798
00:30:00,520 --> 00:30:03,520
他们将拥有100、400个供应商。

799
00:30:03,520 --> 00:30:06,280
只是忘记查询，400个供应商。

800
00:30:06,280 --> 00:30:08,280
惊人的。

801
00:30:08,280 --> 00:30:11,920
然后，他们只是向我们发送每个过程的发送

802
00:30:11,920 --> 00:30:15,360
对于一个供应商，一个供应商在调用绑定时。

803
00:30:15,360 --> 00:30:18,520
然后，他们现在可以切换到其他任何东西。

804
00:30:18,520 --> 00:30:20,120
因此，每当他们想启动ASN时，

805
00:30:20,120 --> 00:30:21,520
他们只是保留那件事。

806
00:30:21,520 --> 00:30:24,520
但是他们必须派遣400封电子邮件才能让他们知道。

807
00:30:24,520 --> 00:30:26,000
这很容易。

808
00:30:26,000 --> 00:30:29,440
一次，那个。

809
00:30:29,440 --> 00:30:29,960
是的。

810
00:30:29,960 --> 00:30:33,280
无论如何，我会把它留给你。

811
00:30:33,280 --> 00:30:36,680
这些是可以以形式输入的一些细节。

812
00:30:36,680 --> 00:30:37,800
假设供应商。

813
00:30:37,800 --> 00:30:39,400
令人震惊的绝对是一种选择。

814
00:30:39,400 --> 00:30:41,000
然后，他们不需要知道任何东西。

815
00:30:41,000 --> 00:30:42,920
那将为他们量身定制，对吗？

816
00:30:42,920 --> 00:30:45,400
如果是通用的，他们将如何

817
00:30:45,400 --> 00:30:47,720
知道供应商代码业务正在保留什么？

818
00:30:47,720 --> 00:30:48,720
他们不需要知道。

819
00:30:48,720 --> 00:30:49,960
这就是我的意思。

820
00:30:49,960 --> 00:30:52,800
它与供应商配置文件相关。

821
00:30:52,800 --> 00:30:56,240
但是供应商资料还必须创建企业，对吗？

822
00:30:56,240 --> 00:30:58,840
因此，当他们加入供应商时，他们会放置代码。

823
00:30:58,920 --> 00:30:59,680
他们将放置一个代码。

824
00:30:59,680 --> 00:31:00,920
使用该代码，您可以查找。

825
00:31:00,920 --> 00:31:01,420
是的。

826
00:31:01,420 --> 00:31:01,920
是的。

827
00:31:01,920 --> 00:31:04,400
它给出了一个个人资料。

828
00:31:04,400 --> 00:31:07,920
我们没有那种知识的部分。

829
00:31:07,920 --> 00:31:09,640
然后，我们将进一步介绍。

830
00:31:09,640 --> 00:31:12,640
但是，这不是我们可以参考的变量吗？

831
00:31:12,640 --> 00:31:16,760
我们正在尝试摆脱所有这些多个对象，

832
00:31:16,760 --> 00:31:17,600
不？

833
00:31:17,600 --> 00:31:19,400
我们可以专注于一个过程？

834
00:31:19,400 --> 00:31:20,800
他们也可以改变它。

835
00:31:20,800 --> 00:31:21,880
但是我同意。

836
00:31:21,880 --> 00:31:23,760
你给我一个价值，我更改它。

837
00:31:23,760 --> 00:31:24,520
不，不，不。

838
00:31:24,520 --> 00:31:26,120
为什么供应商会更改它？

839
00:31:26,120 --> 00:31:28,920
我将不允许客户更改它，不是吗？

840
00:31:28,920 --> 00:31:31,520
Bala在做什么，当您创建供应商时，

841
00:31:31,520 --> 00:31:33,080
在现场，他放了一个ID。

842
00:31:33,080 --> 00:31:33,760
我知道。

843
00:31:33,760 --> 00:31:37,680
这意味着您将其中的用户对象放在那里。

844
00:31:37,680 --> 00:31:38,180
ID。

845
00:31:38,180 --> 00:31:41,000
并将其与之相关联。

846
00:31:41,000 --> 00:31:43,800
当我邀请供应商时，我可能会有一个用户ID或。

847
00:31:43,800 --> 00:31:44,300
是的。

848
00:31:44,300 --> 00:31:47,320
它是在SAP中创建的，我将其放在这里。

849
00:31:47,320 --> 00:31:47,820
是的。

850
00:31:47,820 --> 00:31:51,120
即使我拥有虚拟板，那也很有帮助

851
00:31:51,120 --> 00:31:52,400
对于任何东西。

852
00:31:52,400 --> 00:31:57,680
因此，这意味着整个系统，在这种情况下不支持。

853
00:31:57,680 --> 00:32:00,920
不，也有多个人，我会放置相同的供应商ID。

854
00:32:00,920 --> 00:32:04,240
供应商ID是公司级代码。

855
00:32:04,240 --> 00:32:06,800
但是对于人民来说，巴拉在说什么，

856
00:32:06,800 --> 00:32:08,400
如果您有10个人来自供应商，

857
00:32:08,400 --> 00:32:09,320
您必须放置相同的代码。

858
00:32:09,320 --> 00:32:10,040
是的，您必须放置相同的代码。

859
00:32:10,040 --> 00:32:11,800
所有这些，您必须想到，对吗？

860
00:32:11,800 --> 00:32:13,480
这并不容易。

861
00:32:13,480 --> 00:32:18,080
这意味着整体，这种新的系统流

862
00:32:18,080 --> 00:32:20,760
是针对此SAP情况创建的。

863
00:32:20,800 --> 00:32:22,840
不，不，另一种看待它的方法。

864
00:32:22,840 --> 00:32:25,240
更改用户对象。

865
00:32:25,240 --> 00:32:28,520
该小组呢，无论如何都会引入哪个？

866
00:32:28,520 --> 00:32:32,920
好的，让我通过演示。

867
00:32:32,920 --> 00:32:34,200
我了解问题。

868
00:32:34,200 --> 00:32:37,560
但是无论如何，看看，现在我刚刚创建了这个进步

869
00:32:37,560 --> 00:32:41,160
同一供应商通信骑自行车的节点。

870
00:32:41,160 --> 00:32:42,560
那么谁在看到这个呢？

871
00:32:42,560 --> 00:32:45,080
买家实际上看到了。

872
00:32:45,080 --> 00:32:46,120
买家是？

873
00:32:46,120 --> 00:32:48,360
买方意味着采购团队，

874
00:32:48,360 --> 00:32:52,600
我们用谁创建的人。

875
00:32:52,600 --> 00:32:54,360
这会去SAP吗？

876
00:32:54,360 --> 00:32:56,360
不，目前不是。

877
00:32:56,360 --> 00:32:57,880
他在运输只是一个头脑。

878
00:32:57,880 --> 00:32:58,840
是的，他在运输。

879
00:32:58,840 --> 00:33:02,480
但这是一个很好的东西。

880
00:33:02,480 --> 00:33:05,440
人们不是真的，我的意思是，我们只是展示它。

881
00:33:05,440 --> 00:33:09,160
我在想的是，从一个过程中来说，这非常好。

882
00:33:09,160 --> 00:33:10,520
它看起来很连接。

883
00:33:10,520 --> 00:33:12,680
但是此后业务的关键问题

884
00:33:12,680 --> 00:33:14,120
会看到所有这些，对吗？

885
00:33:14,120 --> 00:33:14,720
是的，是的。

886
00:33:14,720 --> 00:33:16,680
告诉我我所有的供应商前进日本。

887
00:33:16,680 --> 00:33:18,120
我同意，我同意，是的，是的。

888
00:33:18,120 --> 00:33:20,720
告诉我所有的公关。

889
00:33:20,720 --> 00:33:23,400
但是如果需要，请致谢和ASN

890
00:33:23,400 --> 00:33:26,560
我们可以将其作为用户定义的字段发送到SAP。

891
00:33:26,560 --> 00:33:28,640
因为我们有PO号。

892
00:33:28,640 --> 00:33:30,560
我们有所有的东西，所有细节，对吗？

893
00:33:30,560 --> 00:33:32,880
哪个po，哪个po fine项目以及所有这些。

894
00:33:32,880 --> 00:33:34,320
在幕后，我们拥有所有这些。

895
00:33:34,320 --> 00:33:37,720
业务会将我们的系统视为流程系统吗？

896
00:33:37,720 --> 00:33:38,840
过程，过程。

897
00:33:38,840 --> 00:33:41,520
还需要我们构建功能模块吗？

898
00:33:41,520 --> 00:33:44,360
不，不，他们希望所有这些数据都回到SAP。

899
00:33:44,360 --> 00:33:47,360
那么，如果ASN并没有真正使其成为SAP，

900
00:33:47,400 --> 00:33:50,360
那我们也没有理由接受这一点，对吗？

901
00:33:50,360 --> 00:33:53,680
另外，他们会要求我们列出ASN，对吗？

902
00:33:53,680 --> 00:33:55,040
是的，是的。

903
00:33:55,040 --> 00:33:56,480
而他们会看到它。

904
00:33:56,480 --> 00:33:57,640
没错，这是真的。

905
00:33:57,640 --> 00:33:59,480
是的，是的。

906
00:33:59,480 --> 00:34:01,640
如果您连接两个系统，完美，对吗？

907
00:34:01,640 --> 00:34:01,960
是的，是的。

908
00:34:01,960 --> 00:34:03,680
如果您拥有某物，那么我们

909
00:34:03,680 --> 00:34:06,520
也成为来源，对吗？

910
00:34:06,520 --> 00:34:08,800
可以使它有些棘手。

911
00:34:08,800 --> 00:34:09,280
好的。

912
00:34:09,280 --> 00:34:10,520
我完全得到的价值观，巴拉。

913
00:34:10,520 --> 00:34:12,440
我的问题不是疏散。

914
00:34:12,440 --> 00:34:14,600
是的，我将其视为通知。

915
00:34:14,600 --> 00:34:16,920
是的，在您的小组工作空间中，

916
00:34:16,960 --> 00:34:18,400
这是一个完美的设置。

917
00:34:18,400 --> 00:34:21,240
是的，是的，我同意，是的。

918
00:34:21,240 --> 00:34:23,880
但是我认为搜索可能只是一个工作空间搜索，

919
00:34:23,880 --> 00:34:28,320
说，你知道，如果我搜索这个。

920
00:34:28,320 --> 00:34:29,040
这很有意义。

921
00:34:29,040 --> 00:34:31,320
还有任何电子邮件，我们都可以研究它。

922
00:34:31,320 --> 00:34:31,960
是啊是啊。

923
00:34:31,960 --> 00:34:34,880
无论如何，我的意思是，我现在只是显示整个工作流程。

924
00:34:34,880 --> 00:34:35,880
知道了。

925
00:34:35,880 --> 00:34:38,800
因此，现在我准备在仓库中接收商品。

926
00:34:38,800 --> 00:34:40,680
当他们发货和物理货物时

927
00:34:40,680 --> 00:34:43,000
来仓库，好吗？

928
00:34:43,000 --> 00:34:45,280
现在我开始这个过程。

929
00:34:45,280 --> 00:34:48,160
我开始的仓库代表。

930
00:34:48,160 --> 00:34:51,200
仓库代表坐在商业门户网站上。

931
00:34:51,200 --> 00:34:51,800
正确的。

932
00:34:51,800 --> 00:34:54,200
他们应该在外面，不，巴拉？

933
00:34:54,200 --> 00:34:55,400
为什么？

934
00:34:55,400 --> 00:34:59,320
商业和仓库可能是不同的部门，对吗？

935
00:34:59,320 --> 00:35:02,520
是的，是的，但是他们都可以访问一个门户。

936
00:35:02,520 --> 00:35:03,320
他们是工作人员。

937
00:35:03,320 --> 00:35:04,760
他们只是给我们一个起点。

938
00:35:04,760 --> 00:35:07,000
我只是在思考，在现实世界中

939
00:35:07,000 --> 00:35:09,240
他们可能不是真正的组织成员，对吗？

940
00:35:09,240 --> 00:35:12,160
他们将是一个受让人，外部政党，对吗？

941
00:35:12,160 --> 00:35:13,000
不，不，不。

942
00:35:13,000 --> 00:35:15,600
仓库是该组织的雇员，对吗？

943
00:35:15,600 --> 00:35:18,280
员工，但他们不是控制者。

944
00:35:18,280 --> 00:35:19,760
过程。

945
00:35:19,760 --> 00:35:22,800
因此，您可以开始使用。

946
00:35:22,800 --> 00:35:23,640
你可以做到。

947
00:35:23,640 --> 00:35:24,840
是的，您可以做到。

948
00:35:24,840 --> 00:35:25,560
你可以做到。

949
00:35:25,560 --> 00:35:27,360
因此，仓库的一个开始链接。

950
00:35:27,360 --> 00:35:27,860
是的。

951
00:35:36,400 --> 00:35:38,400
所以这是这样做的吧？

952
00:35:38,400 --> 00:35:40,080
所以现在我必须选择项目。

953
00:35:40,080 --> 00:35:43,400
我之所以这样做，是因为没有办法选择项目，对吗？

954
00:35:43,400 --> 00:35:46,560
一秒钟，巴拉，我可以回去吗？

955
00:35:46,560 --> 00:35:48,520
是的，我来自这里。

956
00:35:48,520 --> 00:35:49,360
启动我的背。

957
00:35:49,360 --> 00:35:51,240
好吧，启动我的背，好的。

958
00:35:51,240 --> 00:35:53,880
然后，仓库的家伙必须首先选择PO。

959
00:35:53,880 --> 00:35:56,600
实际上，他必须选择发送的供应商，

960
00:35:56,600 --> 00:35:58,200
因为他会知道他发送了哪个供应商。

961
00:35:58,200 --> 00:36:00,000
但是，如果我选择PO，则供应商应该已经存在。

962
00:36:00,000 --> 00:36:00,960
那也很好。

963
00:36:00,960 --> 00:36:02,280
但是我已经这样做了。

964
00:36:02,280 --> 00:36:04,200
是的，只是检查。

965
00:36:04,200 --> 00:36:08,920
他们会有吗？

966
00:36:08,920 --> 00:36:12,720
还是他们可以将多个POS放入一个GRN中？

967
00:36:12,720 --> 00:36:14,440
他们可以在一个GRN中进行多个POS。

968
00:36:14,440 --> 00:36:17,200
好的，所以PO不是当时的关键。

969
00:36:17,200 --> 00:36:19,200
grn是，grn是。

970
00:36:19,200 --> 00:36:22,080
我们可以为一个PO强制执行一个GRN。

971
00:36:22,080 --> 00:36:23,120
一个po？

972
00:36:23,120 --> 00:36:24,320
这也可以。

973
00:36:24,320 --> 00:36:25,040
这不是问题。

974
00:36:25,040 --> 00:36:26,720
然后，无论如何，当他们开始时，

975
00:36:26,720 --> 00:36:29,160
该PO可能是流量的输入，对吗？

976
00:36:29,160 --> 00:36:29,680
正确的。

977
00:36:29,680 --> 00:36:31,320
作为变量？

978
00:36:31,320 --> 00:36:32,680
PO，是的，他们可以做到。

979
00:36:32,680 --> 00:36:34,320
那你可以避免整个事情，对吗？

980
00:36:34,320 --> 00:36:35,840
我们已经可以抬头来了，对吗？

981
00:36:35,840 --> 00:36:37,160
是的，是的。

982
00:36:37,200 --> 00:36:41,640
因此，我可以说，为这两个项目创建此GRN，对吗？

983
00:36:41,640 --> 00:36:44,120
但是，他们必须决定哪些项目。

984
00:36:44,120 --> 00:36:44,520
正确的。

985
00:36:44,520 --> 00:36:46,920
因为他们知道，他们知道，他们实际收到了这些物品。

986
00:36:46,920 --> 00:36:48,840
不，我是说，当他们开始流动时，

987
00:36:48,840 --> 00:36:50,800
他们唯一开始流程的唯一一件事

988
00:36:50,800 --> 00:36:51,760
PO号是对吗？

989
00:36:51,760 --> 00:36:52,840
PO号和项目。

990
00:36:52,840 --> 00:36:54,640
他们需要另一种输入方式，对吗？

991
00:36:54,640 --> 00:36:56,040
某些形式或其他形式。

992
00:36:56,040 --> 00:36:57,520
现在您给他们一个快速链接，

993
00:36:57,520 --> 00:36:58,560
因此很容易选择。

994
00:36:58,560 --> 00:37:00,080
正确的。

995
00:37:00,080 --> 00:37:02,440
他们真的需要看到这么多数据吗？

996
00:37:02,440 --> 00:37:03,960
即使在今天，他们也不会看到，对吗？

997
00:37:03,960 --> 00:37:05,920
这是非常可配置的，Manu。

998
00:37:05,960 --> 00:37:08,480
不，但是今天，我们看不到那么多，对吗？

999
00:37:08,480 --> 00:37:10,080
您没有SAP访问，对吗？

1000
00:37:10,080 --> 00:37:11,640
是的，但他需要看到。

1001
00:37:11,640 --> 00:37:13,600
他只会看到那个grn中有什么，对吗？

1002
00:37:13,600 --> 00:37:18,640
不，不，但是如果您现在看到我们带来的数据，

1003
00:37:18,640 --> 00:37:20,200
让我们看一下GRN输入表格。

1004
00:37:20,200 --> 00:37:21,960
我没有显示很多数据。

1005
00:37:21,960 --> 00:37:23,240
让我们看一下。

1006
00:37:23,240 --> 00:37:24,120
不，我不是。

1007
00:37:24,120 --> 00:37:25,720
供应商在那里，对吗？

1008
00:37:25,720 --> 00:37:26,880
不，不，让我们等待。

1009
00:37:26,880 --> 00:37:29,480
请参阅，SAP中的项目代码，项目描述，价格，

1010
00:37:29,480 --> 00:37:31,880
他们需要货币，订单数量。

1011
00:37:31,880 --> 00:37:33,240
这就是他们所拥有的。

1012
00:37:33,240 --> 00:37:35,440
不，我的问题是，目前，什么时候

1013
00:37:35,440 --> 00:37:38,640
他们正在放一个grn，他告诉他们所有这些事情

1014
00:37:38,640 --> 00:37:40,840
在GRN通知中，对吗？

1015
00:37:40,840 --> 00:37:43,480
卡车司机会来给他一个PO号。

1016
00:37:43,480 --> 00:37:44,800
PO号和项目。

1017
00:37:44,800 --> 00:37:45,320
项目。

1018
00:37:45,320 --> 00:37:46,080
是的，是的。

1019
00:37:46,080 --> 00:37:47,800
即使在今天，所有其他信息也

1020
00:37:47,800 --> 00:37:49,680
你不知道，对吗？

1021
00:37:49,680 --> 00:37:51,280
他的工作只是为了记录下来，对吗？

1022
00:37:51,280 --> 00:37:52,520
我们可以轻松改变这一点。

1023
00:37:52,520 --> 00:37:55,640
只是想，他今天有什么？

1024
00:37:55,640 --> 00:37:58,320
因此，即使在以前的一步中，他也可能不会

1025
00:37:58,320 --> 00:38:00,360
需要了解PO的全部细节，对吗？

1026
00:38:00,360 --> 00:38:00,840
是的，是的。

1027
00:38:00,840 --> 00:38:02,040
他所知道的只是PO号。

1028
00:38:02,040 --> 00:38:03,880
PO号和项目，是的。

1029
00:38:03,880 --> 00:38:04,560
项目。

1030
00:38:04,560 --> 00:38:06,920
他真正在grn中看到的东西。

1031
00:38:06,920 --> 00:38:09,080
所以我们只是捕获grn，对吗？

1032
00:38:09,080 --> 00:38:10,360
我认为可以这样做。

1033
00:38:10,360 --> 00:38:12,840
不，不，GRN是仓库创建的文档。

1034
00:38:12,840 --> 00:38:13,480
理解。

1035
00:38:13,480 --> 00:38:14,600
那就是他们提出的。

1036
00:38:14,600 --> 00:38:16,720
那会发送发货通知，不是吗？

1037
00:38:16,720 --> 00:38:19,880
和货物，他会发送一份纸质副本说：

1038
00:38:19,880 --> 00:38:22,880
在此PO上，我已经将此项目运送了。

1039
00:38:22,880 --> 00:38:24,600
现在，他们必须调和。

1040
00:38:24,600 --> 00:38:29,240
因此，在同一辆卡车中，您也可以交付多个POS。

1041
00:38:29,240 --> 00:38:32,320
因此，从技术上进行调和，只是预订它。

1042
00:38:32,320 --> 00:38:32,800
预订，是的。

1043
00:38:32,840 --> 00:38:35,240
这就是斯坦利展示的那种风格。

1044
00:38:35,240 --> 00:38:37,960
PO编号，项目和数量。

1045
00:38:37,960 --> 00:38:39,760
在这里，我们需要一张支票说

1046
00:38:39,760 --> 00:38:43,480
这不应该超过待处理的数量。

1047
00:38:43,480 --> 00:38:44,560
接收数量。

1048
00:38:44,560 --> 00:38:46,080
这是一个运行时验证。

1049
00:38:46,080 --> 00:38:47,960
验证我们必须做。

1050
00:38:47,960 --> 00:38:49,240
现在，我还没有完成。

1051
00:38:49,240 --> 00:38:51,440
我的意思是，是的，是的。

1052
00:38:51,440 --> 00:38:52,800
所以我说需要QC。

1053
00:38:52,800 --> 00:38:53,280
就是这样。

1054
00:38:53,280 --> 00:38:54,360
这就是我所能做的。

1055
00:38:54,360 --> 00:38:55,880
假设我们发送了错误的数量。

1056
00:38:55,880 --> 00:38:57,480
SAP会拒绝它吗？

1057
00:38:57,480 --> 00:39:00,160
不，不，他们接受任何东西。

1058
00:39:00,160 --> 00:39:01,800
那你是说我们的价值是什么？

1059
00:39:01,800 --> 00:39:05,760
不，不，SAP也表现得好像什么都不知道。

1060
00:39:05,760 --> 00:39:07,840
如果您不验证，我们不会添加价值。

1061
00:39:07,840 --> 00:39:09,560
它成为纯数据输入。

1062
00:39:09,560 --> 00:39:11,760
不，但是看，这样看，对吗？

1063
00:39:11,760 --> 00:39:13,720
您可以，供应商实际上可能

1064
00:39:13,720 --> 00:39:16,360
发货超过您订购的东西。

1065
00:39:16,360 --> 00:39:17,440
那是可能的。

1066
00:39:17,440 --> 00:39:19,360
因此，我们必须查看基于风险的用例。

1067
00:39:19,360 --> 00:39:24,000
因此，与该项目列表一起，如果我还可以拿桌子，

1068
00:39:24,000 --> 00:39:26,200
我是在现场，而不是UI桌子。

1069
00:39:26,200 --> 00:39:30,280
对于每个项目，我知道收到什么，正在待处理，

1070
00:39:30,360 --> 00:39:32,800
什么是描述，无论我需要什么，

1071
00:39:32,800 --> 00:39:35,560
然后，我甚至可以将其用于验证。

1072
00:39:35,560 --> 00:39:39,640
如果我输入100，那么只有20个即将接收。

1073
00:39:39,640 --> 00:39:40,560
看看是否有问题。

1074
00:39:43,360 --> 00:39:46,760
假设我从中收到20个。

1075
00:39:46,760 --> 00:39:48,920
为此，我没有。

1076
00:39:48,920 --> 00:39:54,840
因此，如果有任何质量检查项目，则标记为“是”

1077
00:39:54,840 --> 00:39:56,760
然后QC过程开始。

1078
00:39:56,760 --> 00:39:58,520
到目前为止，史丹利太好了？

1079
00:39:58,520 --> 00:39:59,520
是的。

1080
00:39:59,520 --> 00:40:03,360
因此，作为一名质量保证的人，我只需要回顾一下，然后说，

1081
00:40:03,360 --> 00:40:05,440
这个质量控制通过了吗？

1082
00:40:05,440 --> 00:40:07,680
实际上，从技术上讲，可能会发生什么

1083
00:40:07,680 --> 00:40:09,880
他们也可以拒绝部分数量吗

1084
00:40:09,880 --> 00:40:11,040
但是我们没有处理。

1085
00:40:11,040 --> 00:40:13,600
我们现在不需要添加它。

1086
00:40:13,600 --> 00:40:14,760
知道了。

1087
00:40:14,760 --> 00:40:17,880
所以我去了这里，对吗？

1088
00:40:17,880 --> 00:40:21,200
然后，SAP应该拥有该信息，对吗？

1089
00:40:21,200 --> 00:40:22,480
是的，我没有对此建模。

1090
00:40:22,480 --> 00:40:23,480
但这是可能的，对吗？

1091
00:40:23,480 --> 00:40:25,160
是的，是的，是的，是的。

1092
00:40:25,160 --> 00:40:26,640
这都就像在它上面构建一样。

1093
00:40:26,640 --> 00:40:29,160
是的，我必须做一个研讨会才能理解

1094
00:40:30,000 --> 00:40:32,000
他们想要的以及所有这些。

1095
00:40:32,000 --> 00:40:33,000
无论如何，这是完成的。

1096
00:40:33,000 --> 00:40:34,200
现在我这样做了。

1097
00:40:34,200 --> 00:40:37,000
它将创建JRN-PO和SAP。

1098
00:40:37,000 --> 00:40:42,160
从请求完成整个循环

1099
00:40:42,160 --> 00:40:44,080
致Jrns。

1100
00:40:44,080 --> 00:40:46,840
这对他们来说是一个主要的痛点，对吗？

1101
00:40:46,840 --> 00:40:47,600
正确，正确。

1102
00:40:47,600 --> 00:40:48,760
是的，是的。

1103
00:40:48,760 --> 00:40:56,440
现在，如果我去这里，我可以看到有新的阅读。

1104
00:40:56,480 --> 00:40:58,680
我对这些家伙感到非常困惑。

1105
00:41:01,280 --> 00:41:02,280
现在，让我们看看。

1106
00:41:07,920 --> 00:41:09,200
让我们关闭这个。

1107
00:41:09,200 --> 00:41:11,680
现在我去货物收据po。

1108
00:41:11,680 --> 00:41:13,040
我去最后一个。

1109
00:41:13,040 --> 00:41:16,400
因此，您将看到我们收到的30和20。

1110
00:41:16,400 --> 00:41:18,040
现在有一个刚刚创建的。

1111
00:41:18,040 --> 00:41:19,240
是的，是的。

1112
00:41:19,240 --> 00:41:21,400
您可以在此处查看采购订单参考。

1113
00:41:21,400 --> 00:41:22,360
您是如何打电话给SAP Bala的？

1114
00:41:22,360 --> 00:41:24,440
来自Mozilla Sap，那是什么故事？

1115
00:41:24,440 --> 00:41:25,520
API，是吗？

1116
00:41:25,520 --> 00:41:28,520
您必须为业务开什么电话。

1117
00:41:28,520 --> 00:41:31,960
一个可以获取会话ID的人，就像我们的访问令牌一样。

1118
00:41:31,960 --> 00:41:33,880
一个是有效载荷。

1119
00:41:33,880 --> 00:41:34,440
那是一篇文章。

1120
00:41:34,440 --> 00:41:37,680
而且，他揭露了我们的MaxPost ID。

1121
00:41:37,680 --> 00:41:40,400
执行相同的DI层。

1122
00:41:40,400 --> 00:41:41,160
这很容易。

1123
00:41:41,160 --> 00:41:44,080
他完成了，如果我告诉他一些改变，

1124
00:41:44,080 --> 00:41:45,600
他会在10分钟内完成。

1125
00:41:45,600 --> 00:41:47,360
因此，API等等，他可以暴露。

1126
00:41:47,360 --> 00:41:48,120
是的，是的。

1127
00:41:48,120 --> 00:41:49,240
API很容易。

1128
00:41:49,240 --> 00:41:50,440
任何对象，他都可以暴露。

1129
00:41:50,440 --> 00:41:53,600
是的，任何对象。

1130
00:41:53,600 --> 00:41:55,800
现在，您也可以看到基础文档。

1131
00:41:55,800 --> 00:41:56,680
它在那里。

1132
00:41:56,680 --> 00:41:58,480
请参阅，相关文档，基本文档，

1133
00:41:58,480 --> 00:42:00,920
您可以在此处转到采购订单。

1134
00:42:00,920 --> 00:42:03,760
所以这一切都是链接的，斯坦利。

1135
00:42:03,760 --> 00:42:06,000
所以他们可以，这一切都做得正确

1136
00:42:06,000 --> 00:42:08,920
这样您就可以获得基本文档。

1137
00:42:08,920 --> 00:42:11,440
您也可以根据购买来查看这里的评论

1138
00:42:11,440 --> 00:42:12,960
因为这个我们没有输入。

1139
00:42:12,960 --> 00:42:16,200
正确发送信息后，

1140
00:42:16,200 --> 00:42:19,680
SAP将做所有的言论以及所有这些。

1141
00:42:19,680 --> 00:42:22,360
现在都是关于抽象互动的

1142
00:42:22,400 --> 00:42:24,000
除了模型，对吗？

1143
00:42:24,000 --> 00:42:24,840
是的。

1144
00:42:24,840 --> 00:42:26,440
从哪里开始，要输入的内容，什么

1145
00:42:26,440 --> 00:42:29,080
发送并获得连接的所有内容将在那里。

1146
00:42:30,200 --> 00:42:31,040
最有可能的，

1147
00:42:32,560 --> 00:42:35,640
这是专业服务

1148
00:42:35,640 --> 00:42:37,680
我们已经使用了很长时间了。

1149
00:42:37,680 --> 00:42:42,400
因此，这更多是关于模型框架的

1150
00:42:42,400 --> 00:42:46,600
如何建立自己的专业服务团队

1151
00:42:46,600 --> 00:42:49,160
更容易被定制。

1152
00:42:49,160 --> 00:42:50,000
是的。

1153
00:42:50,960 --> 00:42:55,200
是的，他们应该只能配置字段

1154
00:42:55,200 --> 00:42:56,760
它应该起作用。

1155
00:42:56,760 --> 00:42:58,080
我们可以做到这一点。

1156
00:42:58,080 --> 00:42:58,920
是的。

1157
00:42:58,920 --> 00:43:00,240
因此，最后，您有一个过程。

1158
00:43:00,240 --> 00:43:01,080
是啊是啊。

1159
00:43:02,240 --> 00:43:03,080
是啊是啊。

1160
00:43:07,880 --> 00:43:10,440
或合作伙伴可以配置史丹利。

1161
00:43:10,440 --> 00:43:12,280
如果您这样做，是的，是的。

1162
00:43:12,280 --> 00:43:14,680
如果您使它变得容易，对吗？

1163
00:43:14,680 --> 00:43:16,920
然后我们训练伴侣，

1164
00:43:16,920 --> 00:43:19,080
他们自己可以实施Boxo。

1165
00:43:20,000 --> 00:43:24,080
因此，这种工作没有自助服务。

1166
00:43:24,080 --> 00:43:25,040
很难，斯坦利，

1167
00:43:25,040 --> 00:43:27,440
因为企业非常不同

1168
00:43:27,440 --> 00:43:28,920
他们如何使用系统。

1169
00:43:28,920 --> 00:43:30,000
这不是我们的错。

1170
00:43:30,000 --> 00:43:34,520
我们正在为我们的专业服务而构建它

1171
00:43:34,520 --> 00:43:35,360
和伴侣。

1172
00:43:36,440 --> 00:43:39,880
我们要去的程度是多少，

1173
00:43:41,040 --> 00:43:43,760
就像他们只能理解某些语言，对吗？

1174
00:43:43,760 --> 00:43:46,480
我们使用，因此，很明显。

1175
00:43:46,480 --> 00:43:49,160
但是那些像供应商这样的对象呢

1176
00:43:49,160 --> 00:43:50,000
喜欢...

1177
00:43:51,560 --> 00:43:54,040
我们没有形式。

1178
00:43:54,040 --> 00:43:58,240
我们将与SAP和形式相关。

1179
00:43:58,240 --> 00:44:00,240
这就是我要问的。

1180
00:44:00,240 --> 00:44:02,600
我们可以有供应商入职并创建供应商。

1181
00:44:02,600 --> 00:44:05,200
如果需要，我们可以提出一套用例。

1182
00:44:05,200 --> 00:44:06,040
好吧，例如斯坦利。

1183
00:44:06,040 --> 00:44:07,400
一组工作流，对吗？

1184
00:44:07,400 --> 00:44:08,240
是的。

1185
00:44:08,240 --> 00:44:10,760
对于供应商打入董事会，我们可以创建一个工作流程。

1186
00:44:10,760 --> 00:44:11,840
这也将是一个流程。

1187
00:44:11,840 --> 00:44:13,360
是的，这将是一个流程。

1188
00:44:13,360 --> 00:44:14,640
然后在它结束时

1189
00:44:14,640 --> 00:44:16,800
我们将发送SAP创建一个供应商。

1190
00:44:16,800 --> 00:44:17,640
是的。

1191
00:44:17,640 --> 00:44:18,480
是的。

1192
00:44:18,640 --> 00:44:20,160
我们如何使用这些？

1193
00:44:20,160 --> 00:44:22,560
没有任何这样的...

1194
00:44:22,560 --> 00:44:25,200
我们以我们的工作方式知道这一点。

1195
00:44:25,200 --> 00:44:29,800
我们的系统应该是在这两个点之间连接，

1196
00:44:29,800 --> 00:44:34,800
业务与领域或SAP系统之间的差距

1197
00:44:36,480 --> 00:44:38,360
以及当前过程的其余部分。

1198
00:44:38,360 --> 00:44:40,760
就像我们应该数字化所有这些电子邮件一样。

1199
00:44:40,760 --> 00:44:41,600
是的。

1200
00:44:41,600 --> 00:44:42,440
好的。

1201
00:44:42,440 --> 00:44:45,080
我们永远不要成为跟踪数据的源系统。

1202
00:44:45,080 --> 00:44:45,920
是啊是啊。

1203
00:44:45,920 --> 00:44:47,560
那是我们应该非常小心的一件事。

1204
00:44:47,560 --> 00:44:48,400
正确，正确。

1205
00:44:48,400 --> 00:44:49,240
是啊是啊。

1206
00:44:49,240 --> 00:44:50,060
因为如果他们说，

1207
00:44:50,060 --> 00:44:51,280
我想跟踪您系统中的ARNS，

1208
00:44:51,280 --> 00:44:52,560
那是一个截然不同的时间表。

1209
00:44:52,560 --> 00:44:53,400
正确，正确。

1210
00:44:53,400 --> 00:44:54,240
是的，是的。

1211
00:44:54,240 --> 00:44:55,080
我们不应该。

1212
00:44:55,080 --> 00:44:55,920
我们不应该。

1213
00:44:55,920 --> 00:45:00,160
即使是流程，我们也不会自定义。

1214
00:45:01,360 --> 00:45:06,120
我们不为一个特定系统自定义

1215
00:45:06,120 --> 00:45:11,120
但为任何类似系统提供一般功能。

1216
00:45:12,120 --> 00:45:15,160
我们可以发出查询。

1217
00:45:16,320 --> 00:45:19,160
我们可以发出查询以撰写，喂食。

1218
00:45:19,160 --> 00:45:21,400
当我们阅读时，我们会支持读者。

1219
00:45:21,400 --> 00:45:24,360
表是支持读者的方式。

1220
00:45:24,360 --> 00:45:28,080
但是一个是视图，我们可以显示一个表。

1221
00:45:28,080 --> 00:45:30,400
我们可以将其放入倒计时。

1222
00:45:30,400 --> 00:45:31,240
是的。

1223
00:45:32,520 --> 00:45:35,160
所以对我们来说，一切都是...

1224
00:45:35,160 --> 00:45:36,280
输入和输出。

1225
00:45:36,280 --> 00:45:37,240
是的，输入和输出。

1226
00:45:37,240 --> 00:45:40,760
但是，依赖项的输入类型

1227
00:45:41,080 --> 00:45:43,560
第一个输入到第二个输入。

1228
00:45:43,560 --> 00:45:44,400
是的。

1229
00:45:44,400 --> 00:45:49,400
因此，它不取决于输入输出类型。

1230
00:45:49,840 --> 00:45:52,760
我们有连接器类型。

1231
00:45:53,880 --> 00:45:55,960
如果我们有可能没想到的东西

1232
00:45:55,960 --> 00:46:00,000
我们可能不会在计算机内使用它。

1233
00:46:00,000 --> 00:46:01,280
连接器。

1234
00:46:01,280 --> 00:46:04,920
当我说连接器时，这意味着自动化连接器。

1235
00:46:04,920 --> 00:46:06,160
应用逻辑。

1236
00:46:06,160 --> 00:46:07,000
那。

1237
00:46:07,000 --> 00:46:09,080
是指碰撞逻辑中的那些。

1238
00:46:09,120 --> 00:46:11,360
获取PO，更新PO。

1239
00:46:11,360 --> 00:46:12,920
创新应用程序。

1240
00:46:12,920 --> 00:46:15,040
自动化应用程序，触发应用程序。

1241
00:46:15,040 --> 00:46:16,520
逻辑。

1242
00:46:16,520 --> 00:46:18,160
是的，是的。

1243
00:46:18,160 --> 00:46:19,760
他们唯一可以建造它。

1244
00:46:19,760 --> 00:46:23,880
然后我们的服务部分...

1245
00:46:23,880 --> 00:46:25,560
为他们配置它。

1246
00:46:25,560 --> 00:46:28,720
为...配置它

1247
00:46:28,720 --> 00:46:30,480
因此，我们显示标准演示。

1248
00:46:30,480 --> 00:46:34,280
然后我们进入了差距分析研讨会

1249
00:46:34,280 --> 00:46:35,640
并配置其流程。

1250
00:46:35,640 --> 00:46:36,480
是的。

1251
00:46:40,080 --> 00:46:40,920
所以这个...

1252
00:46:44,800 --> 00:46:45,640
是的。

1253
00:46:46,520 --> 00:46:47,760
不，如果您把它交给伴侣，

1254
00:46:47,760 --> 00:46:49,720
他们会日复一日地做。

1255
00:46:49,720 --> 00:46:51,840
因此，如果您准备好合作伙伴，不是吗？

1256
00:46:51,840 --> 00:46:53,360
就这样。

1257
00:46:53,360 --> 00:46:57,080
他们会做的，快乐地做服务并赚钱。

1258
00:46:57,080 --> 00:46:58,360
那个流已经问我了，

1259
00:46:58,360 --> 00:46:59,920
您将分享多少收入？

1260
00:47:00,920 --> 00:47:02,200
西班牙。

1261
00:47:02,200 --> 00:47:03,040
西班牙。

1262
00:47:03,040 --> 00:47:03,880
是的。

1263
00:47:03,880 --> 00:47:06,640
就像，我说20％的许可证。

1264
00:47:06,640 --> 00:47:08,240
我说，是的，好的。

1265
00:47:08,280 --> 00:47:11,920
如果您知道，我们会做这项工作怎么办？

1266
00:47:11,920 --> 00:47:13,320
我说，好吧，10％。

1267
00:47:15,320 --> 00:47:16,840
我觉得...的一件事...

1268
00:47:19,160 --> 00:47:20,920
推荐协议和经销商。

1269
00:47:20,920 --> 00:47:23,160
我的意思是，我们有一个经销商协议。

1270
00:47:23,160 --> 00:47:26,360
他们问我们要做多少收入分享。

1271
00:47:26,360 --> 00:47:27,200
他们这样做。

1272
00:47:27,200 --> 00:47:28,600
我们给出10％。

1273
00:47:28,600 --> 00:47:29,440
是啊是啊。

1274
00:47:30,320 --> 00:47:31,640
不，不。

1275
00:47:31,640 --> 00:47:32,800
他们指的是我们。

1276
00:47:32,800 --> 00:47:33,760
他们指的是我们。

1277
00:47:33,760 --> 00:47:36,520
我们给出了我们制造的10％。

1278
00:47:36,520 --> 00:47:37,360
是啊是啊。

1279
00:47:38,560 --> 00:47:39,640
如果他们这样做。

1280
00:47:40,720 --> 00:47:43,880
如果他们卖掉整个事情并做到这一点，

1281
00:47:43,880 --> 00:47:45,440
然后我们给他们20％。

1282
00:47:46,760 --> 00:47:48,760
这取决于我们的收费。

1283
00:47:48,760 --> 00:47:49,800
是的。

1284
00:47:49,800 --> 00:47:51,360
我们可以给他们一个价格清单

1285
00:47:51,360 --> 00:47:53,200
并说，这就是您需要说的。

1286
00:47:53,200 --> 00:47:55,080
但是我们的费用与我们的包裹相同，对吗？

1287
00:47:55,080 --> 00:47:55,920
是的，是的。

1288
00:47:55,920 --> 00:47:58,960
如果他们发现是否有...

1289
00:47:58,960 --> 00:48:02,200
这就是我们需要讨论的，斯坦利。

1290
00:48:02,200 --> 00:48:03,520
他们准备这样做，对吗？

1291
00:48:03,520 --> 00:48:04,360
是的。

1292
00:48:04,360 --> 00:48:05,600
因为那将是巨大的销售率。

1293
00:48:05,600 --> 00:48:06,440
是的，是的。

1294
00:48:06,440 --> 00:48:07,600
这将是。

1295
00:48:07,600 --> 00:48:09,000
但这是服务的永久率。

1296
00:48:09,000 --> 00:48:10,560
当他们进行销售时，

1297
00:48:10,560 --> 00:48:12,840
您分享多少收入？

1298
00:48:12,840 --> 00:48:14,080
不，我们实际上很感兴趣

1299
00:48:14,080 --> 00:48:16,880
在他们现有的客户群斯坦利。

1300
00:48:16,880 --> 00:48:18,280
他们有5,000 ...

1301
00:48:18,280 --> 00:48:19,720
他们有销售。

1302
00:48:19,720 --> 00:48:21,040
是的，他们必须做。

1303
00:48:21,040 --> 00:48:21,880
是的，Upsell。

1304
00:48:21,880 --> 00:48:23,120
他们可能会以附加功能出售。

1305
00:48:23,120 --> 00:48:23,960
附加，是的。

1306
00:48:23,960 --> 00:48:25,440
是的，他们正在向您付款

1307
00:48:25,440 --> 00:48:27,040
您正在与我们分享。

1308
00:48:27,040 --> 00:48:28,280
就像，假设...

1309
00:48:28,280 --> 00:48:29,320
就是这样。

1310
00:48:29,320 --> 00:48:30,160
他们只会出售。

1311
00:48:30,160 --> 00:48:31,360
先生，他们会卖掉它。

1312
00:48:31,360 --> 00:48:32,200
合作伙伴出售。

1313
00:48:32,200 --> 00:48:33,040
是的，合作伙伴卖。

1314
00:48:33,040 --> 00:48:33,880
不，不。

1315
00:48:33,880 --> 00:48:34,720
他们通常会...我们...

1316
00:48:34,720 --> 00:48:35,560
就是这样。

1317
00:48:35,560 --> 00:48:37,760
他们会以20％的价格出售它们。

1318
00:48:37,760 --> 00:48:40,120
如果他们真的很好，也许30％

1319
00:48:40,120 --> 00:48:41,400
他们击中了一些数字。

1320
00:48:41,400 --> 00:48:42,760
他们的方式，斯坦利，

1321
00:48:42,760 --> 00:48:44,640
像任何经销商一样，不是吗？

1322
00:48:46,040 --> 00:48:47,800
如果他们只是指的是

1323
00:48:47,800 --> 00:48:49,640
他们会给10％。

1324
00:48:49,640 --> 00:48:51,040
Palmstack给我们10％。

1325
00:48:51,040 --> 00:48:53,280
我们向他们提交了一些交易。

1326
00:48:53,280 --> 00:48:55,920
我们从中获得了10％。

1327
00:48:55,920 --> 00:48:58,040
然后，您走得更高。

1328
00:48:58,040 --> 00:49:00,200
就像，他们进行完整的销售。

1329
00:49:01,560 --> 00:49:03,880
然后我们得到20％

1330
00:49:03,880 --> 00:49:06,240
因为他们正在冒险。

1331
00:49:06,240 --> 00:49:08,880
那么，多长时间？

1332
00:49:08,880 --> 00:49:10,680
是的，无论年一年...

1333
00:49:10,680 --> 00:49:13,120
我们可以持有一些条款。

1334
00:49:13,120 --> 00:49:13,960
是啊是啊。

1335
00:49:13,960 --> 00:49:15,520
因为那是他们激励的方式

1336
00:49:15,520 --> 00:49:18,160
也要保留客户。

1337
00:49:18,160 --> 00:49:19,160
我的意思是，他们也会为客户提供服务。

1338
00:49:19,160 --> 00:49:20,520
对于Palmstack来说，这很容易

1339
00:49:20,520 --> 00:49:22,200
因为它们也反复出现。

1340
00:49:22,200 --> 00:49:23,040
是的，是的。

1341
00:49:23,040 --> 00:49:24,760
SAP模型可能略有不同，不是吗？

1342
00:49:24,760 --> 00:49:25,600
不，不。

1343
00:49:25,600 --> 00:49:26,720
我们正在向指参考，不是吗？

1344
00:49:26,720 --> 00:49:27,560
是的。

1345
00:49:27,560 --> 00:49:29,640
但是这些家伙每人都会售出一次。

1346
00:49:29,640 --> 00:49:30,480
不。

1347
00:49:30,480 --> 00:49:31,320
与AMC。

1348
00:49:31,320 --> 00:49:32,200
他们会，

1349
00:49:32,200 --> 00:49:34,400
但是我们只会重复收取，不是吗？

1350
00:49:34,400 --> 00:49:36,520
无论如何，我们的成本将比他们低得多。

1351
00:49:36,520 --> 00:49:37,360
正确的。

1352
00:49:37,360 --> 00:49:40,080
SAP ONE就像数以百万计。

1353
00:49:40,080 --> 00:49:40,920
不，不。

1354
00:49:40,920 --> 00:49:42,560
业务不是那么贵，

1355
00:49:42,560 --> 00:49:43,400
但是...

1356
00:49:43,400 --> 00:49:44,960
应该是百万人数，是吗？

1357
00:49:44,960 --> 00:49:45,800
应该是百万，对吗？

1358
00:49:45,800 --> 00:49:46,640
不，不，不。

1359
00:49:46,640 --> 00:49:47,480
不？

1360
00:49:47,480 --> 00:49:49,280
它比...更昂贵

1361
00:49:49,280 --> 00:49:51,960
业务仅约50,000。

1362
00:49:51,960 --> 00:49:53,520
真的吗？

1363
00:49:53,520 --> 00:49:54,360
永久化，

1364
00:49:54,360 --> 00:49:56,560
但是看，业务的问题是

1365
00:49:56,560 --> 00:49:57,440
他们无能为力。

1366
00:49:57,440 --> 00:50:01,480
看，只有一张桌子，根本没有过程。

1367
00:50:01,520 --> 00:50:02,360
没有过程。

1368
00:50:02,360 --> 00:50:03,320
你知道，不是吗？

1369
00:50:03,320 --> 00:50:04,480
根本没有过程。

1370
00:50:04,480 --> 00:50:07,720
这只是一个桌面后表的桌子。

1371
00:50:07,720 --> 00:50:08,560
就这样。

1372
00:50:08,560 --> 00:50:11,040
所以这是一个很好的开始，不是吗？

1373
00:50:11,040 --> 00:50:11,880
是的。

1374
00:50:12,720 --> 00:50:13,920
您可以与他人一起运行同样的事情。

1375
00:50:13,920 --> 00:50:15,680
好吧，让我这样说。

1376
00:50:15,680 --> 00:50:17,440
无论我们引用什么，不是吗？

1377
00:50:17,440 --> 00:50:19,080
$ 28,000，全部

1378
00:50:19,080 --> 00:50:21,720
人们准备接受。

1379
00:50:21,720 --> 00:50:23,240
所以，每年，对吗？

1380
00:50:24,960 --> 00:50:28,240
所以我会说20,000至30,000

1381
00:50:28,240 --> 00:50:29,960
对于中型的小型

1382
00:50:30,000 --> 00:50:31,840
我的意思是，就像一个中型。

1383
00:50:31,840 --> 00:50:33,800
如果您上市市场，您将获得更多。

1384
00:50:35,520 --> 00:50:36,800
实际上，我说的是ARR。

1385
00:50:36,800 --> 00:50:39,640
您显示的是基本过程，对吗？

1386
00:50:39,640 --> 00:50:41,240
因此，没有这些，他们就无法生存。

1387
00:50:41,240 --> 00:50:42,080
所以你做的越多...

1388
00:50:42,080 --> 00:50:43,520
他们在Excel中做什么？

1389
00:50:44,640 --> 00:50:46,000
改变生活。

1390
00:50:46,000 --> 00:50:48,880
但是，我仍然没有说服一件事，巴拉，

1391
00:50:48,880 --> 00:50:50,240
毕竟，

1392
00:50:50,240 --> 00:50:52,480
他们应该有一个非常好的系统可以跟踪，对吗？

1393
00:50:52,480 --> 00:50:54,880
SAP现在看起来像是该系统。

1394
00:50:54,880 --> 00:50:57,880
是的，因为看，数据在SAP中存在。

1395
00:50:57,880 --> 00:51:00,560
这只是...

1396
00:51:00,560 --> 00:51:01,720
过程。

1397
00:51:01,720 --> 00:51:04,240
但是，如果SAP没有某些事情，

1398
00:51:04,240 --> 00:51:05,680
那我们也没有，对吧？

1399
00:51:05,680 --> 00:51:06,520
那就是这样。

1400
00:51:06,520 --> 00:51:08,080
正确，正确，是的，是的。

1401
00:51:08,080 --> 00:51:09,400
例如，像Arn一样。

1402
00:51:09,400 --> 00:51:12,120
如果SAP无法跟踪，我们也无法运行该过程，对吗？

1403
00:51:12,120 --> 00:51:12,960
正确，正确。

1404
00:51:12,960 --> 00:51:15,480
但是，如果您对SAP有交叉引用，

1405
00:51:15,480 --> 00:51:16,440
像那些数字一样，

1406
00:51:16,440 --> 00:51:19,120
现在我们将其存储在工作区堆栈中，

1407
00:51:19,120 --> 00:51:19,960
然后他们可以说，

1408
00:51:19,960 --> 00:51:22,840
我在这个日期收到了这个grn，对吗？

1409
00:51:22,840 --> 00:51:24,160
让我搜索它。

1410
00:51:24,160 --> 00:51:26,480
例如，质量检查数据，对吗？

1411
00:51:26,480 --> 00:51:30,720
没有地方将其存储在SAP中。

1412
00:51:30,720 --> 00:51:31,560
只有在那里

1413
00:51:31,560 --> 00:51:34,040
质量管理模块仅在...

1414
00:51:34,040 --> 00:51:37,760
他们可以以通用的方式使用我们的系统。

1415
00:51:37,760 --> 00:51:40,120
我们将没有任何自定义列表，例如PO列表或...

1416
00:51:40,120 --> 00:51:40,960
不，不，不。

1417
00:51:40,960 --> 00:51:44,280
这就是他们要寻找的grn，

1418
00:51:44,280 --> 00:51:46,480
然后他们可以看到工作空间中的细节，

1419
00:51:46,480 --> 00:51:49,680
说，这就是发生的事情，这些就是质量。

1420
00:51:49,680 --> 00:51:53,480
可悲的是，在我们的溢价中，

1421
00:51:53,480 --> 00:51:55,320
我不知道你对此有何看法，

1422
00:51:56,120 --> 00:51:58,240
假设有很多流程被执行，

1423
00:51:58,240 --> 00:52:02,920
许多数据正在创建，表单，电子科学，pdf。

1424
00:52:02,920 --> 00:52:04,680
我们应该有某种方法来...

1425
00:52:04,680 --> 00:52:06,640
那是我们的内容列表。

1426
00:52:06,640 --> 00:52:08,560
当然，以我们自己的通用方式

1427
00:52:08,560 --> 00:52:10,840
没有任何功能。

1428
00:52:10,840 --> 00:52:13,680
因此，表格列表，文档列表，

1429
00:52:13,680 --> 00:52:16,080
即使在我们的平台中也可以使用，对吗？

1430
00:52:17,280 --> 00:52:18,680
我认为那是网。

1431
00:52:18,680 --> 00:52:19,520
是的。

1432
00:52:19,520 --> 00:52:20,960
当然，这是未来。

1433
00:52:21,000 --> 00:52:25,000
因为我们本身有很多过程，所以

1434
00:52:25,000 --> 00:52:26,600
我们必须回到它。

1435
00:52:26,600 --> 00:52:27,440
是啊是啊。

1436
00:52:27,440 --> 00:52:29,720
我同意，但并非一切都在做...

1437
00:52:29,720 --> 00:52:31,720
所有这些形式，所有这些幸福，

1438
00:52:31,720 --> 00:52:36,240
数据应返回其原始系统。

1439
00:52:36,240 --> 00:52:37,560
那是理想的情况。

1440
00:52:37,560 --> 00:52:39,920
但是对于中小企业，他们可能...

1441
00:52:39,920 --> 00:52:44,600
我们在这里，因为我们必须恢复您的过程。

1442
00:52:44,600 --> 00:52:47,440
如果有任何人想运行...

1443
00:52:47,440 --> 00:52:49,560
不，我只是说...

1444
00:52:49,560 --> 00:52:51,400
不管我们做什么

1445
00:52:51,400 --> 00:52:53,200
这只意味着有必要。

1446
00:52:53,200 --> 00:52:54,640
我的意思是，他们会没有。

1447
00:52:54,640 --> 00:52:56,840
不，我完全同意。

1448
00:52:56,840 --> 00:52:58,880
如果有源或目标系统，

1449
00:52:58,880 --> 00:53:00,400
绝对应该走。

1450
00:53:00,400 --> 00:53:02,680
诸如供应商登机之类的东西，对吗？

1451
00:53:02,680 --> 00:53:05,200
因此，他们必须建立了一些形式。

1452
00:53:05,200 --> 00:53:07,120
如果他们没有CRM，

1453
00:53:07,120 --> 00:53:10,840
他们正在使用Moxor发送供应商入职过程。

1454
00:53:10,840 --> 00:53:12,640
所以他们不需要回去的地方

1455
00:53:12,640 --> 00:53:14,720
然后搜索他的建造？

1456
00:53:14,720 --> 00:53:18,200
他们应该有自动化来放入书或纸上。

1457
00:53:18,240 --> 00:53:20,840
是的，这就是我们当时做出的决定。

1458
00:53:20,840 --> 00:53:21,920
我们永远不会...

1459
00:53:21,920 --> 00:53:23,160
但这就是我们拥有的数据。

1460
00:53:23,160 --> 00:53:25,040
我们知道这是什么。

1461
00:53:25,040 --> 00:53:27,680
我们有一种方法不想将其作为价值提供。

1462
00:53:27,680 --> 00:53:30,720
我们希望提供更多的过程价值。

1463
00:53:30,720 --> 00:53:32,640
关于项目的所有事情，

1464
00:53:32,640 --> 00:53:34,920
不超越项目。

1465
00:53:34,920 --> 00:53:37,600
因此，内容搜索希望使用它。

1466
00:53:37,600 --> 00:53:38,800
综合体。

1467
00:53:38,800 --> 00:53:39,640
和域。

1468
00:53:39,640 --> 00:53:43,320
甚至社区，甚至是最好的工作世界，

1469
00:53:43,320 --> 00:53:45,880
当他们管理它们时。

1470
00:53:45,880 --> 00:53:47,520
我不是在说供应商管理

1471
00:53:47,520 --> 00:53:49,040
作为功​​能。

1472
00:53:49,040 --> 00:53:50,760
表格列表。

1473
00:53:50,760 --> 00:53:51,760
文件列表。

1474
00:53:52,680 --> 00:53:53,520
设计列表。

1475
00:53:53,520 --> 00:53:54,640
我们可以讨论，

1476
00:53:54,640 --> 00:53:56,560
即使从实用性的角度来看。

1477
00:53:57,960 --> 00:53:59,120
当您在公用事业的前面时，

1478
00:53:59,120 --> 00:54:03,720
例如，没有一些返回的应用程序。

1479
00:54:03,720 --> 00:54:04,960
因为这些列表，

1480
00:54:04,960 --> 00:54:06,480
您无法管理它。

1481
00:54:06,480 --> 00:54:10,080
您无法从中获得实用程序。

1482
00:54:10,080 --> 00:54:11,200
那就是，你知道，

1483
00:54:11,200 --> 00:54:12,320
如果您有薪水，

1484
00:54:12,320 --> 00:54:16,000
您没有办法说自己得到一些东西。

1485
00:54:16,000 --> 00:54:18,680
为什么搜索频率

1486
00:54:18,680 --> 00:54:21,440
在此流程图中管理它，

1487
00:54:21,440 --> 00:54:23,080
图表管理，

1488
00:54:23,080 --> 00:54:25,080
而且您将执行数据。

1489
00:54:25,080 --> 00:54:26,840
你会出去的。

1490
00:54:26,840 --> 00:54:27,680
但是为什么不呢？

1491
00:54:27,680 --> 00:54:28,520
如果您不将其列入形式，

1492
00:54:28,520 --> 00:54:29,720
你会出去的。

1493
00:54:29,720 --> 00:54:30,560
您可以将其取出。

1494
00:54:30,560 --> 00:54:31,840
是的，你应该把它取出。

1495
00:54:31,840 --> 00:54:34,280
我认为所有中型尺寸都会很好，

1496
00:54:34,280 --> 00:54:35,600
我们讨论了什么。

1497
00:54:35,600 --> 00:54:38,120
只有一个没有这样的人，

1498
00:54:38,120 --> 00:54:39,280
数据与我们同在

1499
00:54:39,280 --> 00:54:40,120
有什么可以做的吗？

1500
00:54:40,120 --> 00:54:43,600
我们想扩展此处使用的道德过程。

1501
00:54:43,600 --> 00:54:48,600
整个过程与过程相关的内容将是。

1502
00:54:49,040 --> 00:54:49,880
好的。

1503
00:54:49,880 --> 00:54:52,280
现在有一个真正的价值。

1504
00:54:52,280 --> 00:54:53,680
好的。

1505
00:54:53,680 --> 00:54:56,560
然后第二个问题是标签

1506
00:54:56,560 --> 00:54:58,840
过滤工作区。

1507
00:54:58,840 --> 00:55:00,640
我们会把它带入溢价吗？

1508
00:55:00,640 --> 00:55:01,480
标签。

1509
00:55:01,480 --> 00:55:02,320
是的，标签。

1510
00:55:02,320 --> 00:55:03,160
标签。

1511
00:55:03,160 --> 00:55:04,000
标签，标签。

1512
00:55:04,000 --> 00:55:04,840
标签，标签。

1513
00:55:04,840 --> 00:55:05,680
标签。

1514
00:55:05,680 --> 00:55:08,960
是的，用于业务过滤器。

1515
00:55:08,960 --> 00:55:09,800
稍后。

1516
00:55:09,800 --> 00:55:12,600
那也是非常相似的问题。

1517
00:55:12,640 --> 00:55:15,480
因为然后它将开始使他们建立

1518
00:55:15,480 --> 00:55:17,680
他们在我们的工作空间上列出。

1519
00:55:17,680 --> 00:55:18,520
正确的。

1520
00:55:19,400 --> 00:55:21,560
有些，它将起作用。

1521
00:55:21,560 --> 00:55:25,120
就像鲍勃·巴拉（Bob Bala）一样，GRN类型。

1522
00:55:25,120 --> 00:55:26,600
那可能是一部分。

1523
00:55:26,600 --> 00:55:27,600
追踪。

1524
00:55:27,600 --> 00:55:29,440
过程，分类过程。

1525
00:55:29,440 --> 00:55:30,960
是的，排序过程。

1526
00:55:30,960 --> 00:55:32,080
好的。

1527
00:55:32,080 --> 00:55:34,120
因此，我认为我们可能需要。

1528
00:55:34,120 --> 00:55:35,640
那是一个。

1529
00:55:35,640 --> 00:55:38,240
总的来说，我们应该如何看我们的对象

1530
00:55:38,240 --> 00:55:40,560
喜欢用户？

1531
00:55:40,560 --> 00:55:44,680
即使是因为他们的资本，紧迫性也不存在。

1532
00:55:44,680 --> 00:55:47,800
因为来源已经完成，

1533
00:55:47,800 --> 00:55:49,440
它不存在。

1534
00:55:49,440 --> 00:55:51,120
它不是盒子对象的副本。

1535
00:55:51,120 --> 00:55:54,000
是的，如果那是我们看事物的方式，

1536
00:55:54,000 --> 00:55:54,840
然后我认为我们会这样做。

1537
00:55:54,840 --> 00:55:55,680
这不是关键。

1538
00:55:55,680 --> 00:55:56,520
这就是为什么我问这个问题。

1539
00:55:56,520 --> 00:55:57,480
我们只是一项交易。

1540
00:55:57,480 --> 00:55:59,480
是的，我们只是在帮助运行流。

1541
00:55:59,480 --> 00:56:03,360
产品完成后，我们都不重要。

1542
00:56:03,360 --> 00:56:06,200
假设受让人来签名

1543
00:56:06,200 --> 00:56:07,640
我们平台上的文档。

1544
00:56:07,640 --> 00:56:10,200
然后，我们还应该通过电子邮件发送给签名的文件。

1545
00:56:10,200 --> 00:56:12,680
我们不应该持有任何东西，对吗？

1546
00:56:12,680 --> 00:56:13,520
喜欢Docusign。

1547
00:56:16,280 --> 00:56:18,400
您应该纯粹假设要派遣

1548
00:56:18,400 --> 00:56:21,880
就像您应该完成该交易中的图片一样。

1549
00:56:23,880 --> 00:56:26,240
您不会回来查找该电子符号。

1550
00:56:32,240 --> 00:56:34,200
因此，如果有根据交易的输出，

1551
00:56:34,200 --> 00:56:35,040
我们应该发送。

1552
00:56:38,920 --> 00:56:40,080
是的，很明显。

1553
00:56:41,080 --> 00:56:43,200
因为否则，他们可以进入我们的系统

1554
00:56:43,200 --> 00:56:45,560
查找签名时签名的一个。

1555
00:56:45,560 --> 00:56:47,120
当您签名时。

1556
00:56:47,120 --> 00:56:49,240
是的，是的。

1557
00:56:49,240 --> 00:56:50,800
是的。

1558
00:56:50,800 --> 00:56:51,640
得到你。

1559
00:56:51,640 --> 00:56:53,320
是的。

1560
00:56:53,320 --> 00:56:54,840
听起来好像

1561
00:56:57,040 --> 00:56:58,720
这一切都写了。

1562
00:56:58,720 --> 00:57:01,720
因此，让我们看看，听起来像是。
