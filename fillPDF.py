#!/usr/bin/env python3
"""
PDF Form Field Filling Script
Reads numberOfCells_fields.json and fills PDF form fields with values
"""

import json
import sys
from typing import Dict, List, Any, Optional
import PyPDF2
from PyPDF2 import PdfWriter, PdfReader
from PyPDF2.generic import DictionaryObject, ArrayObject, IndirectObject, TextStringObject, NameObject


class PDFFormFiller:
    def __init__(self, pdf_path: str):
        self.pdf_path = pdf_path
        self.reader = None
        self.writer = None
        self.file_handle = None

    def open_pdf(self) -> bool:
        """Open PDF file for reading"""
        try:
            self.file_handle = open(self.pdf_path, 'rb')
            self.reader = PdfReader(self.file_handle)
            self.writer = PdfWriter()
            
            # Clone the document structure
            self.writer.clone_reader_document_root(self.reader)
            
            return True
        except Exception as e:
            print(f"Error opening PDF file: {e}")
            return False

    def close_pdf(self):
        """Close PDF file"""
        if hasattr(self, 'file_handle') and self.file_handle:
            self.file_handle.close()

    def load_field_data(self, json_path: str) -> Dict[str, Any]:
        """Load field data from JSON file"""
        try:
            with open(json_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return data
        except Exception as e:
            print(f"Error loading JSON file: {e}")
            return {}

    def extract_field_values(self, json_data: Dict[str, Any]) -> Dict[str, str]:
        """Extract field names and values from JSON data"""
        field_values = {}
        
        if 'fields' in json_data:
            for field in json_data['fields']:
                if 'value' in field and field['value']:
                    # Try both name and full_name as keys
                    field_name = field.get('name', '')
                    full_name = field.get('full_name', '')
                    value = str(field['value'])
                    
                    if field_name:
                        field_values[field_name] = value
                    if full_name and full_name != field_name:
                        field_values[full_name] = value
                        
                    print(f"Found field to fill: {field_name} = '{value}'")
        
        return field_values

    def fill_form_fields(self, field_values: Dict[str, str]) -> bool:
        """Fill form fields with values"""
        try:
            if not self.writer:
                print("PDF writer not initialized")
                return False

            # Get the form fields from the writer
            if hasattr(self.writer, '_objects') and self.writer._objects:
                # Try to update form fields in the writer
                self._update_form_fields_in_writer(field_values)
            
            return True
            
        except Exception as e:
            print(f"Error filling form fields: {e}")
            import traceback
            traceback.print_exc()
            return False

    def _update_form_fields_in_writer(self, field_values: Dict[str, str]):
        """Update form fields in the PDF writer"""
        # Access the AcroForm from the writer's root object
        if hasattr(self.writer, '_root_object') and self.writer._root_object:
            root = self.writer._root_object
            if '/AcroForm' in root:
                acroform = root['/AcroForm']
                if '/Fields' in acroform:
                    self._fill_fields_recursive(acroform['/Fields'], field_values)

    def _fill_fields_recursive(self, fields, field_values: Dict[str, str], parent_name: str = ""):
        """Recursively fill form fields"""
        if not isinstance(fields, (list, ArrayObject)):
            return
            
        for field_ref in fields:
            try:
                # Resolve field reference
                if hasattr(field_ref, 'get_object'):
                    field = field_ref.get_object()
                else:
                    field = field_ref
                    
                if not isinstance(field, DictionaryObject):
                    continue
                
                # Get field name
                field_name = ""
                if '/T' in field:
                    field_name = str(field['/T'])
                
                # Build full name
                if parent_name:
                    full_name = f"{parent_name}.{field_name}" if field_name else parent_name
                else:
                    full_name = field_name
                
                # Check if we have a value for this field
                value_to_set = None
                if field_name in field_values:
                    value_to_set = field_values[field_name]
                elif full_name in field_values:
                    value_to_set = field_values[full_name]
                
                # Set the field value
                if value_to_set is not None:
                    field[NameObject('/V')] = TextStringObject(value_to_set)
                    print(f"Set field '{field_name}' (full: '{full_name}') to value: '{value_to_set}'")
                
                # Process child fields if they exist
                if '/Kids' in field:
                    self._fill_fields_recursive(field['/Kids'], field_values, full_name)
                    
            except Exception as e:
                print(f"Error processing field: {e}")
                continue

    def save_pdf(self, output_path: str) -> bool:
        """Save the filled PDF to output file"""
        try:
            if not self.writer:
                print("PDF writer not initialized")
                return False
                
            with open(output_path, 'wb') as output_file:
                self.writer.write(output_file)
            
            print(f"Successfully saved filled PDF to: {output_path}")
            return True
            
        except Exception as e:
            print(f"Error saving PDF: {e}")
            import traceback
            traceback.print_exc()
            return False

    def fill_pdf_from_json(self, json_path: str, output_path: str) -> bool:
        """Main method to fill PDF from JSON data"""
        print(f"Loading field data from: {json_path}")
        json_data = self.load_field_data(json_path)
        
        if not json_data:
            print("Failed to load JSON data")
            return False
        
        print(f"Opening PDF file: {self.pdf_path}")
        if not self.open_pdf():
            print("Failed to open PDF file")
            return False
        
        try:
            # Extract field values from JSON
            field_values = self.extract_field_values(json_data)
            
            if not field_values:
                print("No field values found in JSON data")
                return False
            
            print(f"Found {len(field_values)} field values to fill")
            
            # Fill the form fields
            if not self.fill_form_fields(field_values):
                print("Failed to fill form fields")
                return False
            
            # Save the filled PDF
            if not self.save_pdf(output_path):
                print("Failed to save filled PDF")
                return False
            
            return True
            
        finally:
            self.close_pdf()


def main():
    """Main function"""
    # Default file paths
    pdf_path = "fw9.pdf"
    json_path = "numberOfCells_fields.json"
    output_path = "out.pdf"
    
    # Allow command line arguments
    if len(sys.argv) > 1:
        pdf_path = sys.argv[1]
    if len(sys.argv) > 2:
        json_path = sys.argv[2]
    if len(sys.argv) > 3:
        output_path = sys.argv[3]
    
    print(f"PDF Form Filling Tool")
    print(f"Source PDF: {pdf_path}")
    print(f"Field data JSON: {json_path}")
    print(f"Output PDF: {output_path}")
    print("-" * 50)
    
    # Create PDF filler and process
    filler = PDFFormFiller(pdf_path)
    success = filler.fill_pdf_from_json(json_path, output_path)
    
    if success:
        print("\n✓ PDF form filling completed successfully!")
    else:
        print("\n✗ PDF form filling failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
